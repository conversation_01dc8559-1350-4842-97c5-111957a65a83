"""
上下文管理器专题 - 基础示例

演示上下文管理器专题的核心概念和使用方法。
"""


def demonstrate_context_managers():
    """演示上下文管理器"""
    print("=== 上下文管理器演示 ===")
    
    class FileManager:
        def __init__(self, filename, mode):
            self.filename = filename
            self.mode = mode
            self.file = None
        
        def __enter__(self):
            print(f"打开文件: {self.filename}")
            self.file = open(self.filename, self.mode)
            return self.file
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            print(f"关闭文件: {self.filename}")
            if self.file:
                self.file.close()
            if exc_type:
                print(f"处理异常: {exc_type.__name__}: {exc_val}")
            return False  # 不抑制异常
    
    # 使用自定义上下文管理器
    try:
        with FileManager("test.txt", "w") as f:
            f.write("Hello, Context Manager!")
            print("文件写入完成")
    except Exception as e:
        print(f"发生异常: {e}")
    
    # 使用contextlib
    from contextlib import contextmanager
    
    @contextmanager
    def timer():
        import time
        start = time.time()
        print("开始计时...")
        try:
            yield
        finally:
            end = time.time()
            print(f"耗时: {end - start:.4f} 秒")
    
    with timer():
        import time
        time.sleep(0.1)
        print("执行一些操作...")


def demonstrate_additional_concepts():
    """演示其他相关概念"""
    print("\n=== 其他概念演示 ===")
    print("这里可以添加更多相关概念的演示")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_context_managers()
    demonstrate_additional_concepts()
    
    print("\n=== 演示完成 ===")
    print("上下文管理器专题是Python编程的重要概念！")
