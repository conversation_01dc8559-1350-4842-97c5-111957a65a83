# 数据类型深入理解 - 理论概念

## 1. Python类型系统概述

Python是一种动态类型语言，但这并不意味着它没有类型系统。理解Python的类型系统对于编写高质量的代码至关重要。

### 1.1 动态类型 vs 静态类型

**动态类型**：
- 变量的类型在运行时确定
- 同一个变量可以在不同时间指向不同类型的对象
- 类型检查在运行时进行

```python
x = 42        # x指向int对象
x = "hello"   # x现在指向str对象
x = [1, 2, 3] # x现在指向list对象
```

**强类型**：
- Python是强类型语言，不允许隐式的类型转换
- 不同类型之间的操作需要显式转换

```python
# 这会引发TypeError
"3" + 4  # 错误：不能将str和int相加

# 需要显式转换
"3" + str(4)  # 正确："34"
int("3") + 4  # 正确：7
```

## 2. 对象模型

### 2.1 一切皆对象

在Python中，所有的数据都是对象，包括数字、字符串、函数、类等。每个对象都有：

- **身份 (Identity)**：对象在内存中的唯一标识，可以用`id()`函数获取
- **类型 (Type)**：对象的类型，决定了对象支持的操作，可以用`type()`函数获取
- **值 (Value)**：对象存储的数据

```python
x = 42
print(f"身份: {id(x)}")      # 身份: 140712234567456
print(f"类型: {type(x)}")    # 类型: <class 'int'>
print(f"值: {x}")           # 值: 42
```

### 2.2 可变性 (Mutability)

Python对象分为两类：

**不可变对象 (Immutable)**：
- 创建后不能修改
- 包括：int, float, str, tuple, frozenset, bool, None
- 修改操作会创建新对象

**可变对象 (Mutable)**：
- 创建后可以修改
- 包括：list, dict, set, bytearray
- 修改操作在原对象上进行

```python
# 不可变对象示例
s = "hello"
s_id = id(s)
s += " world"  # 创建新字符串对象
print(id(s) == s_id)  # False，不同的对象

# 可变对象示例
lst = [1, 2, 3]
lst_id = id(lst)
lst.append(4)  # 在原对象上修改
print(id(lst) == lst_id)  # True，同一个对象
```

## 3. 数字类型详解

### 3.1 整数类型 (int)

Python 3中的int类型具有任意精度，只受内存限制：

```python
# 小整数缓存 (-5 to 256)
a = 100
b = 100
print(a is b)  # True，使用缓存的对象

# 大整数
big_num = 10**100
print(type(big_num))  # <class 'int'>
```

**内存优化**：
- 小整数 (-5 到 256) 被缓存，相同值共享对象
- 大整数每次创建新对象

### 3.2 浮点数类型 (float)

基于IEEE 754双精度浮点数标准：

```python
# 精度限制
print(0.1 + 0.2 == 0.3)  # False
print(0.1 + 0.2)         # 0.30000000000000004

# 使用decimal模块获得精确计算
from decimal import Decimal
print(Decimal('0.1') + Decimal('0.2') == Decimal('0.3'))  # True
```

### 3.3 复数类型 (complex)

```python
z = 3 + 4j
print(z.real)  # 3.0
print(z.imag)  # 4.0
print(abs(z))  # 5.0
```

## 4. 字符串类型详解

### 4.1 Unicode支持

Python 3中的字符串是Unicode字符串：

```python
s = "Hello, 世界!"
print(len(s))           # 9 (字符数，不是字节数)
print(s.encode('utf-8')) # b'Hello, \xe4\xb8\x96\xe7\x95\x8c!'
```

### 4.2 字符串intern机制

Python会自动intern某些字符串以节省内存：

```python
# 标识符样式的字符串会被intern
a = "hello"
b = "hello"
print(a is b)  # True

# 非标识符样式的字符串可能不会被intern
a = "hello world!"
b = "hello world!"
print(a is b)  # 可能是False（实现相关）

# 手动intern
import sys
a = sys.intern("hello world!")
b = sys.intern("hello world!")
print(a is b)  # True
```

### 4.3 字符串不可变性

```python
s = "hello"
s_id = id(s)
s = s.upper()  # 创建新字符串
print(id(s) == s_id)  # False
```

## 5. 容器类型详解

### 5.1 列表 (list)

动态数组实现，支持随机访问：

```python
# 内存预分配
lst = []
for i in range(5):
    lst.append(i)
    print(f"长度: {len(lst)}, 容量: {lst.__sizeof__()}")
```

### 5.2 字典 (dict)

基于哈希表实现：

```python
# Python 3.7+ 保持插入顺序
d = {'a': 1, 'b': 2, 'c': 3}
print(list(d.keys()))  # ['a', 'b', 'c']

# 哈希冲突处理
class BadHash:
    def __hash__(self):
        return 1  # 所有实例都有相同的哈希值
    
    def __eq__(self, other):
        return False  # 所有实例都不相等
```

### 5.3 集合 (set)

基于哈希表的无序不重复元素集合：

```python
s = {1, 2, 3, 3, 3}
print(s)  # {1, 2, 3}

# 只能包含可哈希对象
# s = {[1, 2, 3]}  # TypeError: unhashable type: 'list'
s = {(1, 2, 3)}    # 正确，tuple是可哈希的
```

## 6. 内存管理

### 6.1 引用计数

Python使用引用计数作为主要的内存管理机制：

```python
import sys

x = [1, 2, 3]
print(sys.getrefcount(x))  # 2 (包括getrefcount的临时引用)

y = x
print(sys.getrefcount(x))  # 3

del y
print(sys.getrefcount(x))  # 2
```

### 6.2 循环引用和垃圾回收

```python
import gc

# 创建循环引用
class Node:
    def __init__(self, value):
        self.value = value
        self.ref = None

a = Node(1)
b = Node(2)
a.ref = b
b.ref = a

# 删除外部引用
del a, b

# 手动触发垃圾回收
collected = gc.collect()
print(f"回收了 {collected} 个对象")
```

## 7. 类型转换

### 7.1 隐式转换

Python很少进行隐式类型转换：

```python
# 布尔上下文中的转换
if []:          # 空列表为False
    print("不会执行")

if [1, 2, 3]:   # 非空列表为True
    print("会执行")

# 数字类型之间的转换
result = 3 + 4.5  # int + float -> float
print(type(result))  # <class 'float'>
```

### 7.2 显式转换

```python
# 基本类型转换
int("123")      # 123
float("3.14")   # 3.14
str(42)         # "42"
bool(0)         # False
bool(1)         # True

# 容器类型转换
list("hello")   # ['h', 'e', 'l', 'l', 'o']
tuple([1, 2, 3]) # (1, 2, 3)
set([1, 2, 2, 3]) # {1, 2, 3}
```

## 8. 性能考虑

### 8.1 选择合适的数据类型

```python
# 对于大量数字计算，考虑使用array模块
import array
arr = array.array('i', [1, 2, 3, 4, 5])  # 比list更节省内存

# 对于字符串拼接，考虑使用join
# 慢：
result = ""
for i in range(1000):
    result += str(i)

# 快：
result = "".join(str(i) for i in range(1000))
```

### 8.2 避免不必要的类型转换

```python
# 避免重复转换
numbers = ["1", "2", "3", "4", "5"]

# 慢：
total = 0
for num_str in numbers:
    total += int(num_str)

# 快：
int_numbers = [int(num_str) for num_str in numbers]
total = sum(int_numbers)
```

## 总结

理解Python的数据类型系统是编写高效、正确代码的基础。关键要点：

1. **一切皆对象**：理解对象的身份、类型和值
2. **可变性很重要**：区分可变和不可变对象
3. **内存管理**：了解引用计数和垃圾回收
4. **选择合适的类型**：根据使用场景选择最适合的数据类型
5. **性能考虑**：避免不必要的类型转换和内存分配
