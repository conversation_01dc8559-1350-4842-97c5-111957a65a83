"""
练习1解答：装饰器专题 - 概念理解

详细解释装饰器的核心概念和基本原理。
"""

import functools
import time
from typing import Callable, Any


def explain_decorator_concepts():
    """解释装饰器的核心概念"""
    print("=== 装饰器核心概念解答 ===")
    
    print("\n1. 装饰器的本质 (Nature of Decorators)")
    print("- 装饰器是一个接受函数作为参数并返回函数的高阶函数")
    print("- 装饰器用于在不修改原函数代码的情况下增强函数功能")
    print("- @语法糖是装饰器应用的简化写法")
    
    # 基础装饰器示例
    def basic_decorator(func):
        """基础装饰器"""
        print(f"装饰器正在装饰函数: {func.__name__}")
        
        def wrapper(*args, **kwargs):
            print(f"执行前: 调用 {func.__name__}")
            result = func(*args, **kwargs)
            print(f"执行后: {func.__name__} 返回 {result}")
            return result
        
        return wrapper
    
    # 手动应用装饰器
    def greet(name):
        return f"Hello, {name}!"
    
    decorated_greet = basic_decorator(greet)
    print("手动装饰器应用:")
    result = decorated_greet("Alice")
    
    # 使用@语法糖
    @basic_decorator
    def farewell(name):
        return f"Goodbye, {name}!"
    
    print("\n使用@语法糖:")
    result = farewell("Bob")
    
    print("\n2. 装饰器的执行时机 (Execution Timing)")
    print("- 装饰器在函数定义时执行，而不是调用时")
    print("- 装饰器返回的wrapper函数在每次调用时执行")
    
    def timing_demo_decorator(func):
        print(f"装饰器执行时间: 定义 {func.__name__} 时")
        
        def wrapper(*args, **kwargs):
            print(f"wrapper执行时间: 调用 {func.__name__} 时")
            return func(*args, **kwargs)
        
        return wrapper
    
    print("\n定义被装饰的函数:")
    @timing_demo_decorator
    def sample_function():
        return "sample result"
    
    print("调用被装饰的函数:")
    sample_function()
    
    print("\n3. 保持函数元信息 (Preserving Function Metadata)")
    print("- 装饰器会改变函数的__name__, __doc__等属性")
    print("- 使用functools.wraps保持原函数的元信息")
    
    def without_wraps(func):
        """不使用wraps的装饰器"""
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    
    def with_wraps(func):
        """使用wraps的装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    
    @without_wraps
    def func_without_wraps():
        """原始函数文档"""
        pass
    
    @with_wraps
    def func_with_wraps():
        """原始函数文档"""
        pass
    
    print(f"不使用wraps: __name__ = {func_without_wraps.__name__}")
    print(f"使用wraps: __name__ = {func_with_wraps.__name__}")
    print(f"不使用wraps: __doc__ = {func_without_wraps.__doc__}")
    print(f"使用wraps: __doc__ = {func_with_wraps.__doc__}")


def demonstrate_decorator_types():
    """演示不同类型的装饰器"""
    print("\n=== 装饰器类型演示 ===")
    
    print("\n1. 函数装饰器 (Function Decorators)")
    
    def retry_decorator(max_attempts=3):
        """重试装饰器工厂"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                last_exception = None
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        print(f"尝试 {attempt + 1} 失败: {e}")
                        if attempt < max_attempts - 1:
                            time.sleep(0.1)
                raise last_exception
            return wrapper
        return decorator
    
    @retry_decorator(max_attempts=2)
    def unreliable_function():
        """不稳定的函数"""
        import random
        if random.random() < 0.5:
            raise ValueError("随机失败")
        return "成功!"
    
    print("测试重试装饰器:")
    try:
        result = unreliable_function()
        print(f"结果: {result}")
    except Exception as e:
        print(f"最终失败: {e}")
    
    print("\n2. 类装饰器 (Class Decorators)")
    
    class CountCalls:
        """统计函数调用次数的类装饰器"""
        def __init__(self, func):
            self.func = func
            self.count = 0
            functools.update_wrapper(self, func)
        
        def __call__(self, *args, **kwargs):
            self.count += 1
            print(f"函数 {self.func.__name__} 被调用了 {self.count} 次")
            return self.func(*args, **kwargs)
    
    @CountCalls
    def say_hello(name):
        return f"Hello, {name}!"
    
    print("测试类装饰器:")
    say_hello("Alice")
    say_hello("Bob")
    say_hello("Charlie")
    
    print("\n3. 带参数的装饰器 (Parameterized Decorators)")
    
    def validate_types(**expected_types):
        """类型验证装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 验证位置参数
                import inspect
                sig = inspect.signature(func)
                bound_args = sig.bind(*args, **kwargs)
                bound_args.apply_defaults()
                
                for param_name, expected_type in expected_types.items():
                    if param_name in bound_args.arguments:
                        value = bound_args.arguments[param_name]
                        if not isinstance(value, expected_type):
                            raise TypeError(
                                f"参数 {param_name} 期望类型 {expected_type.__name__}, "
                                f"实际类型 {type(value).__name__}"
                            )
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    @validate_types(x=int, y=int)
    def add_numbers(x, y):
        """加法函数"""
        return x + y
    
    print("测试类型验证装饰器:")
    print(f"add_numbers(3, 4) = {add_numbers(3, 4)}")
    
    try:
        add_numbers("3", 4)
    except TypeError as e:
        print(f"类型错误: {e}")


def demonstrate_decorator_patterns():
    """演示装饰器的常见模式"""
    print("\n=== 装饰器常见模式 ===")
    
    print("\n1. 缓存装饰器 (Caching Decorator)")
    
    def memoize(func):
        """简单的缓存装饰器"""
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = str(args) + str(sorted(kwargs.items()))
            
            if key in cache:
                print(f"缓存命中: {func.__name__}{args}")
                return cache[key]
            
            print(f"计算结果: {func.__name__}{args}")
            result = func(*args, **kwargs)
            cache[key] = result
            return result
        
        wrapper.cache = cache  # 暴露缓存以便检查
        return wrapper
    
    @memoize
    def fibonacci(n):
        """斐波那契数列"""
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)
    
    print("测试缓存装饰器:")
    print(f"fibonacci(5) = {fibonacci(5)}")
    print(f"缓存大小: {len(fibonacci.cache)}")
    
    print("\n2. 多个装饰器的组合 (Multiple Decorators)")
    
    def bold(func):
        """加粗装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            return f"**{result}**"
        return wrapper
    
    def italic(func):
        """斜体装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            return f"*{result}*"
        return wrapper
    
    @bold
    @italic
    def format_text(text):
        return text
    
    print("测试多个装饰器:")
    print(f"format_text('Hello') = {format_text('Hello')}")
    print("装饰器应用顺序: 从下到上，即 bold(italic(format_text))")


def main():
    """主函数"""
    print("装饰器专题 - 练习1解答")
    print("=" * 50)
    
    explain_decorator_concepts()
    demonstrate_decorator_types()
    demonstrate_decorator_patterns()
    
    print("\n=== 总结 ===")
    print("装饰器是Python的强大特性，理解装饰器包括：")
    print("1. 装饰器的本质是高阶函数")
    print("2. 装饰器在函数定义时执行")
    print("3. 使用functools.wraps保持函数元信息")
    print("4. 函数装饰器、类装饰器、参数化装饰器")
    print("5. 常见模式：缓存、重试、验证、日志等")
    print("6. 多个装饰器的组合使用")
    
    print("\n装饰器让代码更加优雅和可维护！")


if __name__ == "__main__":
    main()
