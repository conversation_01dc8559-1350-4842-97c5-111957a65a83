"""
异步编程 - 基础示例

演示异步编程的核心概念和使用方法。
"""


def demonstrate_asyncio():
    """演示异步编程"""
    print("=== 异步编程演示 ===")
    
    import asyncio
    import time
    
    async def async_task(name, delay):
        """异步任务"""
        print(f"任务 {name} 开始")
        await asyncio.sleep(delay)  # 模拟异步IO操作
        print(f"任务 {name} 完成")
        return f"任务 {name} 的结果"
    
    async def main():
        """主异步函数"""
        print("开始异步任务...")
        
        # 并发执行多个任务
        tasks = [
            async_task("A", 1),
            async_task("B", 2),
            async_task("C", 1.5)
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        print(f"所有任务完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"结果: {results}")
    
    # 运行异步程序
    asyncio.run(main())


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_asyncio()
    
    print("\n=== 演示完成 ===")
    print("异步编程是Python并发编程的重要技术！")
