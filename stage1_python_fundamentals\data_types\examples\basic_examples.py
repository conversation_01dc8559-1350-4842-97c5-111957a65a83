"""
数据类型基础示例

演示Python中各种数据类型的基本特性和使用方法。
"""

import sys
from decimal import Decimal
from fractions import Fraction


def demonstrate_number_types():
    """演示数字类型的特性"""
    print("=== 数字类型演示 ===")
    
    # 整数类型
    print("\n1. 整数类型 (int)")
    small_int = 100
    big_int = 10**100
    print(f"小整数: {small_int}, 类型: {type(small_int)}")
    print(f"大整数: {big_int}")
    print(f"大整数类型: {type(big_int)}")
    
    # 小整数缓存测试
    a = 100
    b = 100
    print(f"小整数缓存: a is b = {a is b}")
    
    c = 1000
    d = 1000
    print(f"大整数: c is d = {c is d}")
    
    # 浮点数类型
    print("\n2. 浮点数类型 (float)")
    f1 = 3.14
    f2 = 1e10  # 科学计数法
    print(f"浮点数: {f1}, 科学计数法: {f2}")
    
    # 浮点数精度问题
    print(f"0.1 + 0.2 = {0.1 + 0.2}")
    print(f"0.1 + 0.2 == 0.3: {0.1 + 0.2 == 0.3}")
    
    # 使用Decimal获得精确计算
    print("\n3. 高精度计算 (Decimal)")
    d1 = Decimal('0.1')
    d2 = Decimal('0.2')
    print(f"Decimal('0.1') + Decimal('0.2') = {d1 + d2}")
    print(f"结果等于0.3: {d1 + d2 == Decimal('0.3')}")
    
    # 分数类型
    print("\n4. 分数类型 (Fraction)")
    frac1 = Fraction(1, 3)
    frac2 = Fraction(1, 6)
    print(f"1/3 + 1/6 = {frac1 + frac2}")
    
    # 复数类型
    print("\n5. 复数类型 (complex)")
    z = 3 + 4j
    print(f"复数: {z}")
    print(f"实部: {z.real}, 虚部: {z.imag}")
    print(f"模长: {abs(z)}")


def demonstrate_string_features():
    """演示字符串类型的特性"""
    print("\n=== 字符串类型演示 ===")
    
    # 字符串创建
    print("\n1. 字符串创建")
    s1 = "Hello"
    s2 = 'World'
    s3 = """多行
    字符串"""
    s4 = r"原始字符串\n不转义"
    
    print(f"双引号: {s1}")
    print(f"单引号: {s2}")
    print(f"三引号: {repr(s3)}")
    print(f"原始字符串: {s4}")
    
    # Unicode支持
    print("\n2. Unicode支持")
    unicode_str = "Hello, 世界! 🌍"
    print(f"Unicode字符串: {unicode_str}")
    print(f"字符长度: {len(unicode_str)}")
    print(f"字节长度: {len(unicode_str.encode('utf-8'))}")
    
    # 字符串不可变性
    print("\n3. 字符串不可变性")
    original = "hello"
    original_id = id(original)
    modified = original.upper()
    print(f"原字符串: {original}, ID: {original_id}")
    print(f"修改后: {modified}, ID: {id(modified)}")
    print(f"是同一个对象: {id(original) == id(modified)}")
    
    # 字符串intern
    print("\n4. 字符串intern机制")
    a = "hello"
    b = "hello"
    print(f"标识符样式字符串: a is b = {a is b}")
    
    c = "hello world!"
    d = "hello world!"
    print(f"非标识符样式: c is d = {c is d}")


def demonstrate_container_types():
    """演示容器类型的特性"""
    print("\n=== 容器类型演示 ===")
    
    # 列表 (list)
    print("\n1. 列表 (list) - 可变序列")
    lst = [1, 2, 3]
    print(f"原列表: {lst}, ID: {id(lst)}")
    lst.append(4)
    print(f"添加元素后: {lst}, ID: {id(lst)}")
    print("列表是可变的，ID保持不变")
    
    # 元组 (tuple)
    print("\n2. 元组 (tuple) - 不可变序列")
    tup = (1, 2, 3)
    print(f"元组: {tup}")
    # tup[0] = 10  # 这会引发TypeError
    print("元组是不可变的，不能修改元素")
    
    # 字典 (dict)
    print("\n3. 字典 (dict) - 键值对映射")
    d = {'a': 1, 'b': 2, 'c': 3}
    print(f"字典: {d}")
    print(f"键: {list(d.keys())}")
    print(f"值: {list(d.values())}")
    print("Python 3.7+ 字典保持插入顺序")
    
    # 集合 (set)
    print("\n4. 集合 (set) - 无序不重复元素")
    s = {1, 2, 3, 3, 3}
    print(f"集合: {s}")
    print("重复元素被自动去除")
    
    # frozenset
    print("\n5. 不可变集合 (frozenset)")
    fs = frozenset([1, 2, 3, 3])
    print(f"不可变集合: {fs}")
    print("frozenset是不可变的，可以作为字典的键")


def demonstrate_type_conversion():
    """演示类型转换"""
    print("\n=== 类型转换演示 ===")
    
    # 基本类型转换
    print("\n1. 基本类型转换")
    print(f"int('123') = {int('123')}")
    print(f"float('3.14') = {float('3.14')}")
    print(f"str(42) = {str(42)}")
    print(f"bool(0) = {bool(0)}")
    print(f"bool(1) = {bool(1)}")
    
    # 容器类型转换
    print("\n2. 容器类型转换")
    print(f"list('hello') = {list('hello')}")
    print(f"tuple([1, 2, 3]) = {tuple([1, 2, 3])}")
    print(f"set([1, 2, 2, 3]) = {set([1, 2, 2, 3])}")
    print(f"dict([('a', 1), ('b', 2)]) = {dict([('a', 1), ('b', 2)])}")
    
    # 隐式类型转换
    print("\n3. 隐式类型转换")
    result1 = 3 + 4.5  # int + float -> float
    print(f"3 + 4.5 = {result1}, 类型: {type(result1)}")
    
    result2 = True + 1  # bool + int -> int
    print(f"True + 1 = {result2}, 类型: {type(result2)}")
    
    # 布尔上下文转换
    print("\n4. 布尔上下文转换")
    values = [0, 1, [], [1], {}, {'a': 1}, "", "hello", None]
    for val in values:
        print(f"bool({repr(val)}) = {bool(val)}")


def demonstrate_object_identity():
    """演示对象身份、类型和值"""
    print("\n=== 对象身份、类型和值 ===")
    
    x = 42
    y = 42
    z = 100
    
    print(f"x = {x}, y = {y}, z = {z}")
    print(f"x的身份: {id(x)}")
    print(f"y的身份: {id(y)}")
    print(f"z的身份: {id(z)}")
    print(f"x is y: {x is y}")  # 小整数缓存
    print(f"x == y: {x == y}")  # 值相等
    
    # 列表对象
    list1 = [1, 2, 3]
    list2 = [1, 2, 3]
    list3 = list1
    
    print(f"\nlist1 = {list1}, ID: {id(list1)}")
    print(f"list2 = {list2}, ID: {id(list2)}")
    print(f"list3 = list1, ID: {id(list3)}")
    print(f"list1 is list2: {list1 is list2}")  # 不同对象
    print(f"list1 == list2: {list1 == list2}")  # 值相等
    print(f"list1 is list3: {list1 is list3}")  # 同一对象


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_number_types()
    demonstrate_string_features()
    demonstrate_container_types()
    demonstrate_type_conversion()
    demonstrate_object_identity()
    
    print("\n=== 演示完成 ===")
    print("请仔细观察每个示例的输出，理解不同数据类型的特性。")
