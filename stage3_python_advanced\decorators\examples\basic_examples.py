"""
装饰器基础示例

演示Python装饰器的基本概念和使用方法。
"""

import functools
import time
from typing import Callable, Any


def simple_decorator(func: Callable) -> Callable:
    """最简单的装饰器示例"""
    def wrapper(*args, **kwargs):
        print(f"调用函数 {func.__name__} 之前")
        result = func(*args, **kwargs)
        print(f"调用函数 {func.__name__} 之后")
        return result
    return wrapper


def timing_decorator(func: Callable) -> Callable:
    """计时装饰器"""
    @functools.wraps(func)  # 保持原函数的元数据
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        print(f"{func.__name__} 执行时间: {end_time - start_time:.6f} 秒")
        return result
    return wrapper


def retry_decorator(max_attempts: int = 3):
    """带参数的装饰器 - 重试机制"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        print(f"函数 {func.__name__} 在 {max_attempts} 次尝试后仍然失败")
                        raise e
                    print(f"第 {attempt + 1} 次尝试失败: {e}")
            return None
        return wrapper
    return decorator


def validate_types(**expected_types):
    """类型验证装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 验证参数类型
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            for param_name, expected_type in expected_types.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    if not isinstance(value, expected_type):
                        raise TypeError(
                            f"参数 {param_name} 期望类型 {expected_type.__name__}, "
                            f"实际类型 {type(value).__name__}"
                        )

            return func(*args, **kwargs)
        return wrapper
    return decorator


class CountCalls:
    """类装饰器 - 统计函数调用次数"""
    def __init__(self, func: Callable):
        self.func = func
        self.count = 0
        functools.update_wrapper(self, func)

    def __call__(self, *args, **kwargs):
        self.count += 1
        print(f"函数 {self.func.__name__} 被调用了 {self.count} 次")
        return self.func(*args, **kwargs)


def demonstrate_basic_decorator():
    """演示基础装饰器"""
    print("=== 基础装饰器演示 ===")

    @simple_decorator
    def greet(name: str) -> str:
        return f"Hello, {name}!"

    print("调用被装饰的函数:")
    result = greet("Alice")
    print(f"返回值: {result}")


def demonstrate_timing_decorator():
    """演示计时装饰器"""
    print("\n=== 计时装饰器演示 ===")

    @timing_decorator
    def slow_function():
        """模拟耗时操作"""
        time.sleep(0.1)
        return "操作完成"

    print("调用耗时函数:")
    result = slow_function()
    print(f"返回值: {result}")


def demonstrate_parameterized_decorator():
    """演示带参数的装饰器"""
    print("\n=== 带参数装饰器演示 ===")

    @retry_decorator(max_attempts=3)
    def unreliable_function(success_rate: float = 0.3):
        """模拟不稳定的函数"""
        import random
        if random.random() < success_rate:
            return "成功!"
        else:
            raise Exception("操作失败")

    print("调用不稳定函数:")
    try:
        result = unreliable_function(0.8)
        print(f"最终结果: {result}")
    except Exception as e:
        print(f"最终失败: {e}")


def demonstrate_type_validation():
    """演示类型验证装饰器"""
    print("\n=== 类型验证装饰器演示 ===")

    @validate_types(x=int, y=int)
    def add_numbers(x, y):
        """加法函数，要求参数为整数"""
        return x + y

    print("正确的类型调用:")
    result = add_numbers(5, 3)
    print(f"5 + 3 = {result}")

    print("\n错误的类型调用:")
    try:
        result = add_numbers("5", 3)
    except TypeError as e:
        print(f"类型错误: {e}")


def demonstrate_class_decorator():
    """演示类装饰器"""
    print("\n=== 类装饰器演示 ===")

    @CountCalls
    def say_hello(name: str):
        return f"Hello, {name}!"

    print("多次调用函数:")
    for name in ["Alice", "Bob", "Charlie"]:
        result = say_hello(name)
        print(f"结果: {result}")


def demonstrate_multiple_decorators():
    """演示多个装饰器的使用"""
    print("\n=== 多个装饰器演示 ===")

    @timing_decorator
    @validate_types(n=int)
    @retry_decorator(max_attempts=2)
    def factorial(n: int) -> int:
        """计算阶乘"""
        if n < 0:
            raise ValueError("n必须为非负整数")
        if n <= 1:
            return 1
        return n * factorial(n - 1)

    print("计算阶乘:")
    result = factorial(5)
    print(f"5! = {result}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_basic_decorator()
    demonstrate_timing_decorator()
    demonstrate_parameterized_decorator()
    demonstrate_type_validation()
    demonstrate_class_decorator()
    demonstrate_multiple_decorators()

    print("\n=== 演示完成 ===")
    print("装饰器是Python中强大的功能增强工具！")