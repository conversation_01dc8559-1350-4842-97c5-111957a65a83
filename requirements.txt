# Python进阶学习项目依赖

# Django框架
Django>=4.2.0,<5.0.0
djangorestframework>=3.14.0

# 测试框架
pytest>=7.0.0
pytest-django>=4.5.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0

# 代码质量工具
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.0.0

# 异步编程相关
aiohttp>=3.8.0
asyncio-mqtt>=0.13.0

# 并发编程工具
concurrent-futures>=3.1.1

# 文档生成
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# 开发工具
ipython>=8.0.0
jupyter>=1.0.0

# 性能分析
memory-profiler>=0.60.0
line-profiler>=4.0.0

# 数据处理（用于示例）
numpy>=1.24.0
pandas>=2.0.0

# 网络请求（用于示例）
requests>=2.28.0

# 时间处理
python-dateutil>=2.8.0

# 配置管理
python-decouple>=3.8

# 日志
loguru>=0.7.0
