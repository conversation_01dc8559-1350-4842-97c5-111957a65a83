"""
属性管理（property）- 基础示例
"""
from typing import Any


class Temperature:
    def __init__(self, celsius: float):
        self._celsius = None
        self.celsius = celsius

    @property
    def celsius(self) -> float:
        return self._celsius

    @celsius.setter
    def celsius(self, value: float) -> None:
        if not isinstance(value, (int, float)):
            raise TypeError("celsius 必须为数字")
        if value < -273.15:
            raise ValueError("低于绝对零度")
        self._celsius = float(value)

    @property
    def fahrenheit(self) -> float:
        return self._celsius * 9 / 5 + 32

    @property
    def kelvin(self) -> float:
        return self._celsius + 273.15


class Person:
    def __init__(self, name: str):
        self._name = None
        self.name = name

    @property
    def name(self) -> str:
        return self._name

    @name.setter
    def name(self, value: str) -> None:
        value = value.strip()
        if not value:
            raise ValueError("name 不能为空")
        # 规范化：压缩多余空格，并统一首字母大写
        normalized = " ".join(value.split())
        self._name = normalized.title()

    @property
    def initials(self) -> str:
        return "".join([p[0] for p in self._name.split()])


def demonstrate_basic_usage():
    print("=== property 基础用法 ===")
    t = Temperature(25)
    print(t.celsius, t.fahrenheit, t.kelvin)
    t.celsius = 0
    print(t.celsius, t.fahrenheit, t.kelvin)

    p = Person("  alice bob  ")
    print(p.name, p.initials)


if __name__ == "__main__":
    demonstrate_basic_usage()
    print("\n=== 演示完成 ===")

