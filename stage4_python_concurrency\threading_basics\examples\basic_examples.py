"""
线程基础示例

演示Python中线程的基本概念和使用方法。
"""

import threading
import time
import queue
from concurrent.futures import ThreadPoolExecutor
import random


def demonstrate_basic_threading():
    """演示基础线程创建和使用"""
    print("=== 基础线程演示 ===")

    def worker(name: str, duration: float):
        """工作函数"""
        print(f"线程 {name} 开始工作")
        time.sleep(duration)
        print(f"线程 {name} 完成工作")

    # 创建线程
    threads = []
    for i in range(3):
        t = threading.Thread(target=worker, args=(f"Worker-{i}", random.uniform(1, 3)))
        threads.append(t)
        t.start()

    # 等待所有线程完成
    for t in threads:
        t.join()

    print("所有线程完成")


def demonstrate_thread_with_return_value():
    """演示获取线程返回值"""
    print("\n=== 线程返回值演示 ===")

    def calculate_square(n: int) -> int:
        """计算平方"""
        time.sleep(1)  # 模拟耗时操作
        return n * n

    # 使用ThreadPoolExecutor获取返回值
    with ThreadPoolExecutor(max_workers=3) as executor:
        # 提交任务
        futures = [executor.submit(calculate_square, i) for i in range(1, 6)]

        # 获取结果
        for i, future in enumerate(futures, 1):
            result = future.result()
            print(f"{i}的平方是: {result}")


def demonstrate_thread_synchronization():
    """演示线程同步"""
    print("\n=== 线程同步演示 ===")

    # 使用类来封装共享资源，避免全局变量问题
    class Counter:
        def __init__(self):
            self.value = 0
            self.lock = threading.Lock()

        def increment(self, name: str):
            with self.lock:  # 使用锁保护共享资源
                old_value = self.value
                time.sleep(0.001)  # 模拟处理时间
                self.value = old_value + 1
                print(f"{name}: counter = {self.value}")

    counter = Counter()

    def increment_counter(name: str, times: int, counter_obj: Counter):
        """增加计数器"""
        for _ in range(times):
            counter_obj.increment(name)

    # 创建多个线程同时修改计数器
    threads = []
    for i in range(3):
        t = threading.Thread(target=increment_counter, args=(f"Thread-{i}", 5, counter))
        threads.append(t)
        t.start()

    # 等待所有线程完成
    for t in threads:
        t.join()

    print(f"最终计数器值: {counter.value}")


def demonstrate_producer_consumer():
    """演示生产者-消费者模式"""
    print("\n=== 生产者-消费者演示 ===")

    # 创建队列
    q = queue.Queue(maxsize=5)

    def producer(name: str):
        """生产者"""
        for i in range(5):
            item = f"{name}-item-{i}"
            q.put(item)
            print(f"生产者 {name} 生产了: {item}")
            time.sleep(random.uniform(0.1, 0.5))
        print(f"生产者 {name} 完成")

    def consumer(name: str):
        """消费者"""
        while True:
            try:
                item = q.get(timeout=2)  # 2秒超时
                print(f"消费者 {name} 消费了: {item}")
                time.sleep(random.uniform(0.1, 0.3))
                q.task_done()
            except queue.Empty:
                print(f"消费者 {name} 超时退出")
                break

    # 创建生产者和消费者线程
    producer_thread = threading.Thread(target=producer, args=("Producer-1",))
    consumer_threads = [
        threading.Thread(target=consumer, args=(f"Consumer-{i}",))
        for i in range(2)
    ]

    # 启动线程
    producer_thread.start()
    for t in consumer_threads:
        t.start()

    # 等待完成
    producer_thread.join()
    for t in consumer_threads:
        t.join()


def demonstrate_thread_local_storage():
    """演示线程本地存储"""
    print("\n=== 线程本地存储演示 ===")

    # 创建线程本地存储
    local_data = threading.local()

    def process_data(thread_id: int):
        """处理数据"""
        # 每个线程都有自己的local_data副本
        local_data.value = thread_id * 10
        local_data.name = f"Thread-{thread_id}"

        print(f"线程 {thread_id} 设置了本地数据: value={local_data.value}, name={local_data.name}")

        time.sleep(1)

        # 读取本地数据（不会被其他线程影响）
        print(f"线程 {thread_id} 读取本地数据: value={local_data.value}, name={local_data.name}")

    # 创建多个线程
    threads = []
    for i in range(3):
        t = threading.Thread(target=process_data, args=(i,))
        threads.append(t)
        t.start()

    # 等待所有线程完成
    for t in threads:
        t.join()


def demonstrate_daemon_threads():
    """演示守护线程"""
    print("\n=== 守护线程演示 ===")

    def background_task():
        """后台任务"""
        count = 0
        while True:
            count += 1
            print(f"后台任务运行中... {count}")
            time.sleep(1)

    def main_task():
        """主任务"""
        for i in range(3):
            print(f"主任务执行中... {i+1}")
            time.sleep(1)
        print("主任务完成")

    # 创建守护线程
    daemon_thread = threading.Thread(target=background_task)
    daemon_thread.daemon = True  # 设置为守护线程
    daemon_thread.start()

    # 创建普通线程
    main_thread = threading.Thread(target=main_task)
    main_thread.start()
    main_thread.join()

    print("程序即将退出（守护线程会自动结束）")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_basic_threading()
    demonstrate_thread_with_return_value()
    demonstrate_thread_synchronization()
    demonstrate_producer_consumer()
    demonstrate_thread_local_storage()
    demonstrate_daemon_threads()

    print("\n=== 演示完成 ===")
    print("线程是Python并发编程的重要工具！")