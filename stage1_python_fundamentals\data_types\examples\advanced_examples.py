"""
数据类型深入理解 - 进阶示例

演示Python数据类型的内存行为、性能特征和底层实现细节。
"""

import sys
import time
import gc
import array
import collections
from decimal import Decimal, getcontext
from typing import Any, List, Dict, Optional


def demonstrate_memory_behavior():
    """演示内存行为和对象身份"""
    print("=== 内存行为演示 ===")

    # 1. 小整数缓存池
    print("\n1. 小整数缓存池 (-5 到 256)")
    a = 100
    b = 100
    c = 300
    d = 300
    print(f"a = 100, b = 100: a is b = {a is b}")
    print(f"c = 300, d = 300: c is d = {c is d}")
    print(f"id(a) = {id(a)}, id(b) = {id(b)}")
    print(f"id(c) = {id(c)}, id(d) = {id(d)}")

    # 2. 字符串intern机制
    print("\n2. 字符串intern机制")
    s1 = "hello"
    s2 = "hello"
    s3 = "hello world"
    s4 = "hello world"
    s5 = "hello" + " world"  # 运行时拼接

    print(f"s1 = 'hello', s2 = 'hello': s1 is s2 = {s1 is s2}")
    print(f"s3 = 'hello world', s4 = 'hello world': s3 is s4 = {s3 is s4}")
    print(f"s5 = 'hello' + ' world': s3 is s5 = {s3 is s5}")

    # 3. 列表的内存分配策略
    print("\n3. 列表内存分配策略")
    lst = []
    print("列表容量变化:")
    for i in range(10):
        lst.append(i)
        print(f"长度: {len(lst)}, 容量: {sys.getsizeof(lst)} bytes")

    # 4. 对象引用计数
    print("\n4. 对象引用计数")
    obj = [1, 2, 3]
    print(f"初始引用计数: {sys.getrefcount(obj) - 1}")  # -1因为getrefcount本身会增加引用

    ref1 = obj
    print(f"创建引用后: {sys.getrefcount(obj) - 1}")

    del ref1
    print(f"删除引用后: {sys.getrefcount(obj) - 1}")


def demonstrate_string_interning():
    """演示字符串驻留机制"""
    print("\n=== 字符串驻留演示 ===")

    # 1. 自动驻留的情况
    print("\n1. 自动驻留的字符串")
    cases = [
        ("标识符样式", "hello", "hello"),
        ("数字字符串", "123", "123"),
        ("空字符串", "", ""),
        ("单字符", "a", "a"),
    ]

    for desc, s1, s2 in cases:
        print(f"{desc}: '{s1}' is '{s2}' = {s1 is s2}")

    # 2. 不会自动驻留的情况
    print("\n2. 不会自动驻留的字符串")
    s1 = "hello world!"  # 包含特殊字符
    s2 = "hello world!"
    print(f"包含特殊字符: s1 is s2 = {s1 is s2}")

    # 运行时创建的字符串
    s3 = "hello" + " " + "world!"
    print(f"运行时拼接: s1 is s3 = {s1 is s3}")

    # 3. 手动驻留
    print("\n3. 手动驻留")
    import sys
    s4 = sys.intern("hello world!")
    s5 = sys.intern("hello world!")
    print(f"手动驻留: s4 is s5 = {s4 is s5}")
    print(f"与原字符串: s1 is s4 = {s1 is s4}")

    # 4. 驻留的性能影响
    print("\n4. 字符串比较性能测试")

    # 创建大量字符串
    strings1 = ["test_string_" + str(i) for i in range(1000)]
    strings2 = ["test_string_" + str(i) for i in range(1000)]

    # 普通比较
    start_time = time.time()
    for s1, s2 in zip(strings1, strings2):
        _ = s1 == s2
    normal_time = time.time() - start_time

    # 驻留后比较
    interned1 = [sys.intern(s) for s in strings1]
    interned2 = [sys.intern(s) for s in strings2]

    start_time = time.time()
    for s1, s2 in zip(interned1, interned2):
        _ = s1 is s2  # 身份比较，更快
    interned_time = time.time() - start_time

    print(f"普通字符串比较时间: {normal_time:.6f}s")
    print(f"驻留字符串比较时间: {interned_time:.6f}s")
    if interned_time > 0:
        print(f"性能提升: {normal_time/interned_time:.2f}x")
    else:
        print("驻留字符串比较速度极快，无法准确测量")


def demonstrate_container_performance():
    """演示容器性能对比"""
    print("\n=== 容器性能演示 ===")

    # 1. 不同容器的查找性能
    print("\n1. 查找性能对比")
    data_size = 10000

    # 准备数据
    data_list = list(range(data_size))
    data_tuple = tuple(range(data_size))
    data_set = set(range(data_size))
    data_dict = {i: i for i in range(data_size)}

    target = data_size - 1  # 查找最后一个元素

    # 列表查找
    start_time = time.time()
    for _ in range(100):
        _ = target in data_list
    list_time = time.time() - start_time

    # 元组查找
    start_time = time.time()
    for _ in range(100):
        _ = target in data_tuple
    tuple_time = time.time() - start_time

    # 集合查找
    start_time = time.time()
    for _ in range(100):
        _ = target in data_set
    set_time = time.time() - start_time

    # 字典查找
    start_time = time.time()
    for _ in range(100):
        _ = target in data_dict
    dict_time = time.time() - start_time

    print(f"列表查找时间: {list_time:.6f}s")
    print(f"元组查找时间: {tuple_time:.6f}s")
    print(f"集合查找时间: {set_time:.6f}s")
    print(f"字典查找时间: {dict_time:.6f}s")

    # 2. 内存使用对比
    print("\n2. 内存使用对比")
    n = 1000

    list_obj = list(range(n))
    tuple_obj = tuple(range(n))
    set_obj = set(range(n))
    dict_obj = {i: i for i in range(n)}
    array_obj = array.array('i', range(n))

    print(f"列表内存: {sys.getsizeof(list_obj)} bytes")
    print(f"元组内存: {sys.getsizeof(tuple_obj)} bytes")
    print(f"集合内存: {sys.getsizeof(set_obj)} bytes")
    print(f"字典内存: {sys.getsizeof(dict_obj)} bytes")
    print(f"数组内存: {sys.getsizeof(array_obj)} bytes")

    # 3. 特殊容器类型
    print("\n3. 特殊容器类型")

    # deque vs list 的性能
    from collections import deque

    # 左侧插入性能对比
    test_list = []
    test_deque = deque()

    start_time = time.time()
    for i in range(10000):
        test_list.insert(0, i)
    list_insert_time = time.time() - start_time

    start_time = time.time()
    for i in range(10000):
        test_deque.appendleft(i)
    deque_insert_time = time.time() - start_time

    print(f"列表左侧插入时间: {list_insert_time:.6f}s")
    print(f"deque左侧插入时间: {deque_insert_time:.6f}s")
    if deque_insert_time > 0:
        print(f"deque性能提升: {list_insert_time/deque_insert_time:.2f}x")
    else:
        print("deque插入速度极快，无法准确测量性能提升")


def demonstrate_type_system():
    """演示类型系统和精度控制"""
    print("\n=== 类型系统演示 ===")

    # 1. 数值类型的精度控制
    print("\n1. 数值精度控制")

    # Decimal精度设置
    print("Decimal精度控制:")
    getcontext().prec = 6  # 设置6位精度
    d1 = Decimal('1') / Decimal('3')
    print(f"1/3 (6位精度): {d1}")

    getcontext().prec = 50  # 设置50位精度
    d2 = Decimal('1') / Decimal('3')
    print(f"1/3 (50位精度): {d2}")

    # 2. 类型转换的精度损失
    print("\n2. 类型转换精度损失")

    # 大整数转浮点数
    big_int = 2**53 + 1
    float_val = float(big_int)
    back_to_int = int(float_val)

    print(f"原始大整数: {big_int}")
    print(f"转为浮点数: {float_val}")
    print(f"转回整数: {back_to_int}")
    print(f"精度损失: {big_int != back_to_int}")

    # 3. 动态类型的运行时检查
    print("\n3. 动态类型检查")

    def analyze_type(obj):
        """分析对象的类型信息"""
        print(f"对象: {obj}")
        print(f"类型: {type(obj)}")
        print(f"类型名: {type(obj).__name__}")
        print(f"MRO: {type(obj).__mro__}")
        print(f"是否可调用: {callable(obj)}")
        print(f"是否可迭代: {hasattr(obj, '__iter__')}")
        print(f"是否可哈希: {hasattr(obj, '__hash__') and obj.__hash__ is not None}")
        print("-" * 40)

    # 分析不同类型的对象
    objects = [
        42,
        3.14,
        "hello",
        [1, 2, 3],
        {1, 2, 3},
        lambda x: x,
        analyze_type
    ]

    for obj in objects:
        analyze_type(obj)


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_memory_behavior()
    demonstrate_string_interning()
    demonstrate_container_performance()
    demonstrate_type_system()

    print("\n=== 演示完成 ===")
    print("深入理解数据类型的内存行为和性能特征，有助于编写更高效的Python代码！")