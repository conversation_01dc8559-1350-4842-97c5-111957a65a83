"""
身份vs相等性测试

测试身份vs相等性相关的代码示例和概念。
"""

import pytest
import sys
from io import StringIO

from stage1_python_pitfalls.identity_equality.examples.basic_examples import (
    demonstrate_basic_concept,
    demonstrate_practical_usage,
    demonstrate_common_patterns,
)


def test_integers_identity_and_equality():
    a = 100
    b = 100
    assert a is b
    assert a == b
    x = 1000
    y = 1000
    # CPython 通常不是同一对象，但不强制
    assert x == y


def test_containers_identity_and_equality():
    l1 = [1, 2]
    l2 = [1, 2]
    assert l1 == l2
    assert l1 is not l2
    l3 = l1
    assert l3 is l1


def test_nan_behavior():
    import math
    nan = float('nan')
    assert not (nan == nan)
    assert math.isnan(nan)


def test_demos_callable():
    demonstrate_basic_concept()
    demonstrate_practical_usage()
    demonstrate_common_patterns()


if __name__ == "__main__":
    pytest.main([__file__])
