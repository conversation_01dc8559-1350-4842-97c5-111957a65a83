"""
Django 基础示例（不依赖真实 Django 运行环境，仅用于概念演示）。

包含：
- MTV 与 MVC 的关系
- 最小 URL 与视图概念
- 模板渲染的基本流程
"""

from typing import List


def demonstrate_mtv_mvc_relationship() -> None:
    print("=== Django 基础：MTV 与 MVC ===")
    print("Django 模型：Model-Template-View (MTV)")
    print("对应 MVC：Model-View-Controller (MVC)")
    print("映射：Model→Model, Template→View(表现层), View→Controller(业务逻辑)")


def demonstrate_minimal_url_and_view() -> None:
    print("=== 最小 URL 与视图概念 ===")
    print("urls.py: path('hello/', hello_view)")
    print("views.py: def hello_view(request): return HttpResponse('Hello')")
    print("请求流程：浏览器→URL 路由→视图函数→响应")


def demonstrate_template_rendering() -> None:
    print("=== 模板渲染基础 ===")
    print("模板示例：hello.html 包含 {{ name }} 占位符")
    print("视图：return render(request, 'hello.html', {'name': 'Django'})")
    print("模板引擎：将上下文中的变量替换到模板并返回 HTML")


def demonstrate_app_structure() -> None:
    print("=== Django 应用基本结构 ===")
    structure: List[str] = [
        "myproject/",
        "  manage.py",
        "  myproject/",
        "    settings.py",
        "    urls.py",
        "    wsgi.py",
        "  myapp/",
        "    models.py",
        "    views.py",
        "    urls.py",
        "    templates/",
    ]
    for line in structure:
        print(line)


if __name__ == "__main__":
    demonstrate_mtv_mvc_relationship()
    demonstrate_minimal_url_and_view()
    demonstrate_template_rendering()
    demonstrate_app_structure()
    print("\n(以上为概念性输出，便于在未安装 Django 的环境中学习流程)")



