"""
数据类型示例测试

测试数据类型相关的代码示例和概念。
"""

import pytest
import sys
from io import StringIO
from stage1_python_fundamentals.data_types.examples.basic_examples import (
    demonstrate_number_types,
    demonstrate_string_features,
    demonstrate_container_types,
    demonstrate_type_conversion,
    demonstrate_object_identity
)


class TestNumberTypes:
    """测试数字类型相关功能"""

    def test_integer_caching(self):
        """测试小整数缓存机制"""
        # 小整数应该被缓存
        a = 100
        b = 100
        assert a is b, "小整数应该使用缓存"

        # 大整数不应该被缓存
        c = 1000
        d = 1000
        # 注意：这个测试可能因Python实现而异
        # assert c is not d, "大整数不应该使用缓存"

    def test_float_precision(self):
        """测试浮点数精度问题"""
        result = 0.1 + 0.2
        assert result != 0.3, "浮点数精度问题"
        assert abs(result - 0.3) < 1e-10, "差值应该很小"

    def test_decimal_precision(self):
        """测试Decimal的精确计算"""
        from decimal import Decimal
        d1 = Decimal('0.1')
        d2 = Decimal('0.2')
        result = d1 + d2
        assert result == Decimal('0.3'), "Decimal应该提供精确计算"

    def test_complex_numbers(self):
        """测试复数类型"""
        z = 3 + 4j
        assert z.real == 3.0, "实部应该是3.0"
        assert z.imag == 4.0, "虚部应该是4.0"
        assert abs(z) == 5.0, "模长应该是5.0"


class TestStringFeatures:
    """测试字符串特性"""

    def test_string_immutability(self):
        """测试字符串不可变性"""
        s = "hello"
        original_id = id(s)
        s = s.upper()
        assert id(s) != original_id, "字符串修改应该创建新对象"

    def test_string_interning(self):
        """测试字符串intern机制"""
        a = "hello"
        b = "hello"
        assert a is b, "标识符样式的字符串应该被intern"

    def test_unicode_support(self):
        """测试Unicode支持"""
        s = "Hello, 世界! 🌍"
        assert len(s) == 12, "Unicode字符串长度计算"
        encoded = s.encode('utf-8')
        assert isinstance(encoded, bytes), "编码后应该是bytes类型"


class TestContainerTypes:
    """测试容器类型"""

    def test_list_mutability(self):
        """测试列表可变性"""
        lst = [1, 2, 3]
        original_id = id(lst)
        lst.append(4)
        assert id(lst) == original_id, "列表修改不应该改变对象ID"
        assert lst == [1, 2, 3, 4], "列表内容应该被修改"

    def test_tuple_immutability(self):
        """测试元组不可变性"""
        t = (1, 2, 3)
        with pytest.raises(TypeError):
            t[0] = 10  # 应该抛出TypeError

    def test_dict_ordering(self):
        """测试字典顺序保持（Python 3.7+）"""
        d = {'c': 3, 'a': 1, 'b': 2}
        keys = list(d.keys())
        assert keys == ['c', 'a', 'b'], "字典应该保持插入顺序"

    def test_set_uniqueness(self):
        """测试集合的唯一性"""
        s = {1, 2, 3, 3, 3}
        assert s == {1, 2, 3}, "集合应该自动去重"


class TestTypeConversion:
    """测试类型转换"""

    def test_basic_conversions(self):
        """测试基本类型转换"""
        assert int('123') == 123
        assert float('3.14') == 3.14
        assert str(42) == '42'
        assert bool(0) is False
        assert bool(1) is True

    def test_container_conversions(self):
        """测试容器类型转换"""
        assert list('hello') == ['h', 'e', 'l', 'l', 'o']
        assert tuple([1, 2, 3]) == (1, 2, 3)
        assert set([1, 2, 2, 3]) == {1, 2, 3}

    def test_implicit_conversions(self):
        """测试隐式类型转换"""
        result = 3 + 4.5  # int + float -> float
        assert isinstance(result, float)
        assert result == 7.5

        result = True + 1  # bool + int -> int
        assert isinstance(result, int)
        assert result == 2


class TestObjectIdentity:
    """测试对象身份"""

    def test_identity_vs_equality(self):
        """测试身份vs相等性"""
        a = [1, 2, 3]
        b = [1, 2, 3]
        c = a

        assert a == b, "值应该相等"
        assert a is not b, "但不是同一个对象"
        assert a is c, "c应该是a的引用"

    def test_object_properties(self):
        """测试对象的身份、类型和值"""
        x = 42
        assert isinstance(id(x), int), "id()应该返回整数"
        assert type(x) is int, "type()应该返回正确的类型"
        assert x == 42, "值应该正确"


class TestDemonstrationFunctions:
    """测试演示函数的输出"""

    def test_demonstrate_number_types_output(self, capsys):
        """测试数字类型演示函数的输出"""
        demonstrate_number_types()
        captured = capsys.readouterr()
        assert "数字类型演示" in captured.out
        assert "整数类型" in captured.out
        assert "浮点数类型" in captured.out

    def test_demonstrate_string_features_output(self, capsys):
        """测试字符串特性演示函数的输出"""
        demonstrate_string_features()
        captured = capsys.readouterr()
        assert "字符串类型演示" in captured.out
        assert "Unicode支持" in captured.out
        assert "不可变性" in captured.out

    def test_demonstrate_container_types_output(self, capsys):
        """测试容器类型演示函数的输出"""
        demonstrate_container_types()
        captured = capsys.readouterr()
        assert "容器类型演示" in captured.out
        assert "列表" in captured.out
        assert "字典" in captured.out

    def test_all_demonstrations_run_without_error(self):
        """测试所有演示函数都能正常运行"""
        # 这些函数应该都能正常运行而不抛出异常
        demonstrate_number_types()
        demonstrate_string_features()
        demonstrate_container_types()
        demonstrate_type_conversion()
        demonstrate_object_identity()


if __name__ == "__main__":
    pytest.main([__file__])