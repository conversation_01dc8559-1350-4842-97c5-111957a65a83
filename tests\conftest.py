"""
pytest配置文件

为整个项目的测试提供通用的配置和fixture。
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@pytest.fixture(scope="session")
def project_root_path():
    """返回项目根目录路径"""
    return Path(__file__).parent.parent


@pytest.fixture(scope="function")
def sample_data():
    """提供测试用的示例数据"""
    return {
        'numbers': [1, 2, 3, 4, 5],
        'strings': ['hello', 'world', 'python'],
        'mixed': [1, 'two', 3.0, [4, 5]],
        'dict_data': {'a': 1, 'b': 2, 'c': 3}
    }


@pytest.fixture(scope="function")
def temp_file(tmp_path):
    """创建临时文件用于测试"""
    temp_file = tmp_path / "test_file.txt"
    temp_file.write_text("This is a test file.")
    return temp_file


@pytest.fixture(scope="function")
def capture_output(capsys):
    """捕获标准输出用于测试"""
    def _capture():
        captured = capsys.readouterr()
        return captured.out, captured.err
    return _capture


# 测试标记
pytest_markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
]


def pytest_configure(config):
    """pytest配置"""
    for marker in pytest_markers:
        config.addinivalue_line("markers", marker)


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为没有标记的测试添加unit标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# 自定义断言帮助函数
class AssertionHelpers:
    """测试断言帮助类"""
    
    @staticmethod
    def assert_type_and_value(obj, expected_type, expected_value):
        """断言对象的类型和值"""
        assert isinstance(obj, expected_type), f"Expected type {expected_type}, got {type(obj)}"
        assert obj == expected_value, f"Expected value {expected_value}, got {obj}"
    
    @staticmethod
    def assert_raises_with_message(exception_type, message_pattern, callable_obj, *args, **kwargs):
        """断言抛出特定异常和消息"""
        import re
        with pytest.raises(exception_type) as exc_info:
            callable_obj(*args, **kwargs)
        assert re.search(message_pattern, str(exc_info.value)), \
            f"Exception message '{exc_info.value}' doesn't match pattern '{message_pattern}'"


@pytest.fixture(scope="session")
def assert_helpers():
    """提供断言帮助函数"""
    return AssertionHelpers()
