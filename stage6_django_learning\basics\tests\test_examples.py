"""
Django 基础示例的烟雾测试（不依赖真实 Django 环境）。
"""

from stage6_django_learning.basics.examples.basic_examples import (
    demonstrate_mtv_mvc_relationship,
    demonstrate_minimal_url_and_view,
    demonstrate_template_rendering,
    demonstrate_app_structure,
)


def test_basics_examples_smoke(capsys):
    demonstrate_mtv_mvc_relationship()
    demonstrate_minimal_url_and_view()
    demonstrate_template_rendering()
    demonstrate_app_structure()
    out, err = capsys.readouterr()
    assert "Django 基础" in out
    assert "URL" in out
    assert "模板" in out
    assert "myproject/" in out



