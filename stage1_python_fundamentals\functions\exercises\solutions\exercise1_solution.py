"""
练习1解答：函数机制详解 - 概念理解

详细解释函数机制的核心概念和基本原理。
"""


def explain_function_concepts():
    """解释函数的核心概念"""
    print("=== 函数机制核心概念解答 ===")
    
    print("\n1. 函数对象 (Function Objects)")
    print("- 在Python中，函数是一等公民(first-class objects)")
    print("- 函数可以赋值给变量、作为参数传递、作为返回值")
    print("- 函数有自己的属性，如__name__, __doc__, __module__等")
    
    # 演示函数对象
    def sample_func(x):
        """示例函数"""
        return x * 2
    
    print(f"函数名: {sample_func.__name__}")
    print(f"函数类型: {type(sample_func)}")
    print(f"函数可调用: {callable(sample_func)}")
    
    # 函数赋值
    func_alias = sample_func
    print(f"函数别名调用: {func_alias(5)}")
    
    print("\n2. 参数传递机制 (Parameter Passing)")
    print("- Python使用'传对象引用'的方式传递参数")
    print("- 不可变对象：传递的是对象的引用，但不能修改对象本身")
    print("- 可变对象：传递的是对象的引用，可以修改对象内容")
    
    def modify_immutable(x):
        print(f"  修改前: x = {x}, id = {id(x)}")
        x = x + 10  # 创建新对象
        print(f"  修改后: x = {x}, id = {id(x)}")
        return x
    
    def modify_mutable(lst):
        print(f"  修改前: lst = {lst}, id = {id(lst)}")
        lst.append(4)  # 修改原对象
        print(f"  修改后: lst = {lst}, id = {id(lst)}")
        return lst
    
    # 演示参数传递
    num = 5
    print(f"原始数字: {num}, id = {id(num)}")
    result = modify_immutable(num)
    print(f"函数返回后: num = {num}, result = {result}")
    
    my_list = [1, 2, 3]
    print(f"原始列表: {my_list}, id = {id(my_list)}")
    result = modify_mutable(my_list)
    print(f"函数返回后: my_list = {my_list}")
    
    print("\n3. 作用域和LEGB规则 (Scope and LEGB Rule)")
    print("- L (Local): 局部作用域")
    print("- E (Enclosing): 嵌套作用域")
    print("- G (Global): 全局作用域")
    print("- B (Built-in): 内置作用域")
    print("- Python按照LEGB顺序查找变量")
    
    # 演示作用域
    x = "global"  # 全局作用域
    
    def outer():
        x = "enclosing"  # 嵌套作用域
        
        def inner():
            x = "local"  # 局部作用域
            print(f"    inner中的x: {x}")
        
        inner()
        print(f"  outer中的x: {x}")
    
    outer()
    print(f"全局作用域中的x: {x}")
    
    print("\n4. 闭包 (Closures)")
    print("- 闭包是指内部函数引用了外部函数的变量")
    print("- 即使外部函数已经返回，内部函数仍能访问这些变量")
    print("- 闭包常用于创建工厂函数和装饰器")
    
    def make_multiplier(factor):
        """创建乘法器闭包"""
        def multiplier(number):
            return number * factor  # 引用外部变量factor
        return multiplier
    
    double = make_multiplier(2)
    triple = make_multiplier(3)
    
    print(f"double(5) = {double(5)}")
    print(f"triple(5) = {triple(5)}")
    print(f"double的闭包: {double.__closure__}")
    if double.__closure__:
        print(f"闭包中的factor值: {double.__closure__[0].cell_contents}")
    
    print("\n5. 装饰器基础 (Decorator Basics)")
    print("- 装饰器是修改或增强函数功能的高阶函数")
    print("- 使用@语法糖简化装饰器的应用")
    print("- 装饰器本质上是函数的函数")
    
    def simple_decorator(func):
        """简单装饰器"""
        def wrapper(*args, **kwargs):
            print(f"调用函数: {func.__name__}")
            result = func(*args, **kwargs)
            print(f"函数返回: {result}")
            return result
        return wrapper
    
    @simple_decorator
    def add(a, b):
        return a + b
    
    result = add(3, 4)
    
    print("\n6. 生成器基础 (Generator Basics)")
    print("- 生成器是使用yield关键字的特殊函数")
    print("- 生成器返回迭代器对象，支持惰性求值")
    print("- 内存效率高，适合处理大量数据")
    
    def fibonacci_generator(n):
        """斐波那契数列生成器"""
        a, b = 0, 1
        count = 0
        while count < n:
            yield a
            a, b = b, a + b
            count += 1
    
    print("斐波那契数列前5项:")
    for num in fibonacci_generator(5):
        print(num, end=" ")
    print()


def demonstrate_function_advantages():
    """演示函数的优势"""
    print("\n=== 函数的优势和应用场景 ===")
    
    print("\n1. 代码复用 (Code Reuse)")
    print("- 避免重复代码，提高开发效率")
    print("- 统一处理逻辑，便于维护和修改")
    
    print("\n2. 模块化设计 (Modular Design)")
    print("- 将复杂问题分解为简单的子问题")
    print("- 每个函数负责单一职责")
    print("- 便于测试和调试")
    
    print("\n3. 抽象和封装 (Abstraction and Encapsulation)")
    print("- 隐藏实现细节，提供简洁的接口")
    print("- 降低系统复杂度，提高可读性")
    
    print("\n4. 函数式编程支持 (Functional Programming)")
    print("- 支持高阶函数、闭包、装饰器等特性")
    print("- 便于实现函数式编程范式")


def main():
    """主函数"""
    print("函数机制详解 - 练习1解答")
    print("=" * 50)
    
    explain_function_concepts()
    demonstrate_function_advantages()
    
    print("\n=== 总结 ===")
    print("函数是Python编程的核心概念，理解函数机制包括：")
    print("1. 函数作为对象的特性")
    print("2. 参数传递的引用机制")
    print("3. 作用域和变量查找规则")
    print("4. 闭包的概念和应用")
    print("5. 装饰器和生成器的基础")
    print("6. 函数在软件设计中的重要作用")
    
    print("\n掌握这些概念对于编写高质量的Python代码至关重要！")


if __name__ == "__main__":
    main()
