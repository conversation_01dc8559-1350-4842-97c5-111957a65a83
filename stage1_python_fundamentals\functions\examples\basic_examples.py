"""
函数机制详解 - 基础示例

演示Python中函数的各种特性和使用方法。
"""

import sys
import time
from functools import reduce
from typing import List, Dict, Optional, Union, Callable


def demonstrate_function_as_object():
    """演示函数作为对象的特性"""
    print("=== 函数作为对象演示 ===")

    def greet(name: str) -> str:
        """问候函数"""
        return f"Hello, {name}!"

    # 函数的属性
    print(f"函数名: {greet.__name__}")
    print(f"函数类型: {type(greet)}")
    print(f"函数文档: {greet.__doc__}")
    print(f"函数模块: {greet.__module__}")

    # 函数赋值给变量
    say_hello = greet
    print(f"通过变量调用: {say_hello('Alice')}")

    # 函数存储在数据结构中
    functions = [greet, say_hello]
    print(f"从列表调用: {functions[0]('Bob')}")

    # 函数作为字典值
    operations = {
        'greet': greet,
        'farewell': lambda name: f"Goodbye, {name}!"
    }
    print(f"从字典调用: {operations['greet']('Charlie')}")
    print(f"Lambda函数: {operations['farewell']('<PERSON>')}")


def demonstrate_parameter_passing():
    """演示参数传递机制"""
    print("\n=== 参数传递机制演示 ===")

    def modify_immutable(x: int) -> int:
        """尝试修改不可变对象"""
        print(f"函数内部修改前: x = {x}, id = {id(x)}")
        x = x + 10
        print(f"函数内部修改后: x = {x}, id = {id(x)}")
        return x

    def modify_mutable(lst: List[int]) -> List[int]:
        """修改可变对象"""
        print(f"函数内部修改前: lst = {lst}, id = {id(lst)}")
        lst.append(4)
        print(f"函数内部修改后: lst = {lst}, id = {id(lst)}")
        return lst

    # 不可变对象传递
    print("\n1. 不可变对象传递")
    num = 5
    print(f"调用前: num = {num}, id = {id(num)}")
    result = modify_immutable(num)
    print(f"调用后: num = {num}, id = {id(num)}")
    print(f"返回值: {result}")

    # 可变对象传递
    print("\n2. 可变对象传递")
    my_list = [1, 2, 3]
    print(f"调用前: my_list = {my_list}, id = {id(my_list)}")
    result = modify_mutable(my_list)
    print(f"调用后: my_list = {my_list}, id = {id(my_list)}")
    print(f"返回值: {result}")


def demonstrate_parameter_types():
    """演示各种参数类型"""
    print("\n=== 参数类型演示 ===")

    def complex_function(
        pos_only, /,                    # 仅限位置参数
        pos_or_kw,                      # 位置或关键字参数
        default_param="default",        # 默认参数
        *args,                          # 可变位置参数
        kw_only,                        # 仅限关键字参数
        **kwargs                        # 可变关键字参数
    ):
        """复杂参数函数"""
        print(f"仅限位置参数: {pos_only}")
        print(f"位置或关键字参数: {pos_or_kw}")
        print(f"默认参数: {default_param}")
        print(f"可变位置参数: {args}")
        print(f"仅限关键字参数: {kw_only}")
        print(f"可变关键字参数: {kwargs}")

    print("调用复杂函数:")
    complex_function(
        "pos_only_value",               # 仅限位置
        "pos_or_kw_value",             # 位置参数
        "custom_default",               # 默认参数
        "extra1", "extra2",             # *args
        kw_only="kw_only_value",        # 仅限关键字
        extra_kw1="value1",             # **kwargs
        extra_kw2="value2"
    )


def demonstrate_scope_and_legb():
    """演示作用域和LEGB规则"""
    print("\n=== 作用域和LEGB规则演示 ===")

    x = "global"  # 全局作用域

    def outer():
        x = "enclosing"  # 嵌套作用域
        print(f"outer函数中的x: {x}")

        def inner():
            x = "local"  # 局部作用域
            print(f"inner函数中的x: {x}")

        inner()
        print(f"调用inner后，outer中的x: {x}")

    print(f"全局作用域中的x: {x}")
    outer()
    print(f"调用outer后，全局作用域中的x: {x}")

    # global和nonlocal示例
    print("\n--- global和nonlocal示例 ---")

    def modify_global():
        global x
        x = "modified global"
        print(f"modify_global中修改后的x: {x}")

    def outer_nonlocal():
        x = "enclosing"
        print(f"outer_nonlocal中的x: {x}")

        def modify_enclosing():
            nonlocal x
            x = "modified enclosing"
            print(f"modify_enclosing中修改后的x: {x}")

        modify_enclosing()
        print(f"调用modify_enclosing后，outer_nonlocal中的x: {x}")

    modify_global()
    print(f"调用modify_global后，全局x: {x}")

    outer_nonlocal()


def demonstrate_closures():
    """演示闭包"""
    print("\n=== 闭包演示 ===")

    def make_multiplier(factor: int) -> Callable[[int], int]:
        """创建乘法器闭包"""
        def multiplier(number: int) -> int:
            return number * factor  # 引用外部变量
        return multiplier

    # 创建不同的乘法器
    double = make_multiplier(2)
    triple = make_multiplier(3)

    print(f"double(5) = {double(5)}")
    print(f"triple(5) = {triple(5)}")

    # 检查闭包
    print(f"double的闭包变量: {double.__closure__}")
    if double.__closure__:
        print(f"factor的值: {double.__closure__[0].cell_contents}")

    # 闭包的实际应用 - 计数器
    def make_counter(start: int = 0):
        """创建计数器闭包"""
        count = start

        def counter():
            nonlocal count
            count += 1
            return count

        return counter

    print("\n--- 计数器闭包 ---")
    counter1 = make_counter(0)
    counter2 = make_counter(100)

    print(f"counter1: {counter1()}, {counter1()}, {counter1()}")
    print(f"counter2: {counter2()}, {counter2()}, {counter2()}")


def demonstrate_recursion():
    """演示递归"""
    print("\n=== 递归演示 ===")

    def factorial(n: int) -> int:
        """计算阶乘"""
        if n <= 1:
            return 1
        return n * factorial(n - 1)

    def fibonacci(n: int) -> int:
        """计算斐波那契数列"""
        if n <= 1:
            return n
        return fibonacci(n - 1) + fibonacci(n - 2)

    # 尾递归优化版本
    def factorial_tail(n: int, acc: int = 1) -> int:
        """尾递归阶乘"""
        if n <= 1:
            return acc
        return factorial_tail(n - 1, n * acc)

    print(f"factorial(5) = {factorial(5)}")
    print(f"fibonacci(8) = {fibonacci(8)}")
    print(f"factorial_tail(5) = {factorial_tail(5)}")

    # 递归深度限制
    print(f"当前递归深度限制: {sys.getrecursionlimit()}")


def demonstrate_higher_order_functions():
    """演示高阶函数"""
    print("\n=== 高阶函数演示 ===")

    def apply_operation(numbers: List[int], operation: Callable[[int], int]) -> List[int]:
        """应用操作到数字列表"""
        return [operation(x) for x in numbers]

    def square(x: int) -> int:
        return x ** 2

    def cube(x: int) -> int:
        return x ** 3

    numbers = [1, 2, 3, 4, 5]

    print(f"原始数字: {numbers}")
    print(f"平方: {apply_operation(numbers, square)}")
    print(f"立方: {apply_operation(numbers, cube)}")
    print(f"使用lambda: {apply_operation(numbers, lambda x: x * 2)}")

    # 函数作为返回值
    def make_validator(min_length: int) -> Callable[[str], bool]:
        """创建验证器函数"""
        def validator(text: str) -> bool:
            return len(text) >= min_length
        return validator

    password_validator = make_validator(8)
    username_validator = make_validator(3)

    print(f"\n--- 验证器函数 ---")
    print(f"密码'12345'验证: {password_validator('12345')}")
    print(f"密码'12345678'验证: {password_validator('12345678')}")
    print(f"用户名'ab'验证: {username_validator('ab')}")
    print(f"用户名'abc'验证: {username_validator('abc')}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_function_as_object()
    demonstrate_parameter_passing()
    demonstrate_parameter_types()
    demonstrate_scope_and_legb()
    demonstrate_closures()
    demonstrate_recursion()
    demonstrate_higher_order_functions()

    print("\n=== 演示完成 ===")
    print("函数是Python编程的核心概念！")