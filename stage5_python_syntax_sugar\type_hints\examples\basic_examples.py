"""
类型注解专题 - 基础示例

演示类型注解专题的核心概念和使用方法。
"""


def demonstrate_type_hints():
    """演示类型注解"""
    print("=== 类型注解演示 ===")
    
    from typing import List, Dict, Optional, Union, Callable, TypeVar, Generic
    
    def greet(name: str, age: int) -> str:
        """带类型注解的函数"""
        return f"Hello, {name}! You are {age} years old."
    
    def process_items(items: List[str]) -> Dict[str, int]:
        """处理字符串列表，返回长度字典"""
        return {item: len(item) for item in items}
    
    def find_item(items: List[str], target: str) -> Optional[int]:
        """查找项目，返回索引或None"""
        try:
            return items.index(target)
        except ValueError:
            return None
    
    # 使用类型注解的函数
    result = greet("Alice", 25)
    print(result)
    
    items = ["python", "java", "go"]
    lengths = process_items(items)
    print(f"长度字典: {lengths}")
    
    index = find_item(items, "java")
    print(f"java的索引: {index}")
    
    # 泛型类型
    T = TypeVar('T')
    
    class Stack(Generic[T]):
        def __init__(self) -> None:
            self._items: List[T] = []
        
        def push(self, item: T) -> None:
            self._items.append(item)
        
        def pop(self) -> T:
            return self._items.pop()
    
    # 使用泛型栈
    int_stack: Stack[int] = Stack()
    int_stack.push(1)
    int_stack.push(2)
    print(f"弹出: {int_stack.pop()}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_type_hints()
    
    print("\n=== 演示完成 ===")
    print("类型注解专题让Python代码更加简洁优雅！")
