"""
拷贝机制详解 - 基础示例

演示Python中浅拷贝和深拷贝的区别及其影响。
"""

import copy


def demonstrate_assignment_vs_copy():
    """演示赋值vs拷贝的区别"""
    print("=== 赋值vs拷贝演示 ===")

    # 赋值 - 创建引用
    original_list = [1, 2, [3, 4]]
    assigned_list = original_list  # 赋值，创建引用

    print(f"原始列表: {original_list}")
    print(f"赋值列表: {assigned_list}")
    print(f"是同一个对象: {original_list is assigned_list}")

    # 修改原始列表
    original_list[0] = 'changed'
    original_list[2][0] = 'nested_changed'

    print(f"\n修改原始列表后:")
    print(f"原始列表: {original_list}")
    print(f"赋值列表: {assigned_list}")
    print("结论: 赋值创建的是引用，修改会影响所有引用")


def demonstrate_shallow_copy():
    """演示浅拷贝"""
    print("\n=== 浅拷贝演示 ===")

    # 创建包含嵌套对象的列表
    original = [1, 2, [3, 4], {'a': 5}]

    # 方法1: 使用copy.copy()
    shallow_copy1 = copy.copy(original)

    # 方法2: 使用切片
    shallow_copy2 = original[:]

    # 方法3: 使用list()构造函数
    shallow_copy3 = list(original)

    # 方法4: 使用列表推导式
    shallow_copy4 = [item for item in original]

    print(f"原始列表: {original}")
    print(f"浅拷贝1: {shallow_copy1}")
    print(f"是同一个对象: {original is shallow_copy1}")
    print(f"内容相等: {original == shallow_copy1}")

    # 修改顶层元素
    print(f"\n修改顶层元素:")
    original[0] = 'top_level_changed'
    print(f"原始列表: {original}")
    print(f"浅拷贝1: {shallow_copy1}")
    print("结论: 浅拷贝的顶层元素独立")

    # 修改嵌套对象
    print(f"\n修改嵌套对象:")
    original[2][0] = 'nested_changed'
    original[3]['a'] = 'dict_changed'
    print(f"原始列表: {original}")
    print(f"浅拷贝1: {shallow_copy1}")
    print("结论: 浅拷贝的嵌套对象仍然共享")


def demonstrate_deep_copy():
    """演示深拷贝"""
    print("\n=== 深拷贝演示 ===")

    # 创建复杂的嵌套结构
    original = {
        'name': 'Alice',
        'scores': [85, 90, 78],
        'address': {
            'city': 'Beijing',
            'details': {
                'street': 'Main St',
                'number': 123
            }
        },
        'friends': ['Bob', 'Charlie']
    }

    # 深拷贝
    deep_copied = copy.deepcopy(original)

    print(f"原始字典: {original}")
    print(f"深拷贝字典: {deep_copied}")
    print(f"是同一个对象: {original is deep_copied}")
    print(f"内容相等: {original == deep_copied}")

    # 检查嵌套对象
    print(f"\n嵌套对象检查:")
    print(f"scores是同一个对象: {original['scores'] is deep_copied['scores']}")
    print(f"address是同一个对象: {original['address'] is deep_copied['address']}")
    print(f"深层嵌套是同一个对象: {original['address']['details'] is deep_copied['address']['details']}")

    # 修改各层级的数据
    print(f"\n修改各层级数据:")
    original['name'] = 'Alice_Modified'  # 顶层
    original['scores'].append(95)        # 第二层
    original['address']['city'] = 'Shanghai'  # 第三层
    original['address']['details']['street'] = 'New St'  # 第四层

    print(f"修改后原始字典: {original}")
    print(f"深拷贝字典: {deep_copied}")
    print("结论: 深拷贝完全独立，任何层级的修改都不会相互影响")


def demonstrate_copy_with_custom_objects():
    """演示自定义对象的拷贝"""
    print("\n=== 自定义对象拷贝演示 ===")

    class Person:
        def __init__(self, name, age, friends=None):
            self.name = name
            self.age = age
            self.friends = friends or []

        def __repr__(self):
            return f"Person(name='{self.name}', age={self.age}, friends={self.friends})"

        def __copy__(self):
            """自定义浅拷贝行为"""
            print(f"执行 {self.name} 的浅拷贝")
            return Person(self.name, self.age, self.friends)

        def __deepcopy__(self, memo):
            """自定义深拷贝行为"""
            print(f"执行 {self.name} 的深拷贝")
            return Person(
                copy.deepcopy(self.name, memo),
                copy.deepcopy(self.age, memo),
                copy.deepcopy(self.friends, memo)
            )

    # 创建对象
    alice = Person("Alice", 25, ["Bob", "Charlie"])

    print(f"原始对象: {alice}")

    # 浅拷贝
    alice_shallow = copy.copy(alice)
    print(f"浅拷贝对象: {alice_shallow}")
    print(f"friends是同一个对象: {alice.friends is alice_shallow.friends}")

    # 深拷贝
    alice_deep = copy.deepcopy(alice)
    print(f"深拷贝对象: {alice_deep}")
    print(f"friends是同一个对象: {alice.friends is alice_deep.friends}")

    # 修改friends列表
    alice.friends.append("David")
    print(f"\n修改原始对象的friends后:")
    print(f"原始对象: {alice}")
    print(f"浅拷贝对象: {alice_shallow}")
    print(f"深拷贝对象: {alice_deep}")


def demonstrate_copy_pitfalls():
    """演示拷贝的常见陷阱"""
    print("\n=== 拷贝陷阱演示 ===")

    # 陷阱1: 以为浅拷贝会完全独立
    print("陷阱1: 浅拷贝的嵌套对象共享")
    matrix = [[0] * 3 for _ in range(3)]
    matrix_copy = copy.copy(matrix)

    print(f"原始矩阵: {matrix}")
    print(f"拷贝矩阵: {matrix_copy}")

    # 修改一个元素
    matrix[0][0] = 1
    print(f"修改原始矩阵[0][0]后:")
    print(f"原始矩阵: {matrix}")
    print(f"拷贝矩阵: {matrix_copy}")
    print("说明: 浅拷贝只拷贝顶层容器，内部列表仍然共享")

    # 陷阱2: 循环引用的深拷贝
    print(f"\n陷阱2: 循环引用的处理")

    class Node:
        def __init__(self, value):
            self.value = value
            self.parent = None
            self.children = []

        def add_child(self, child):
            child.parent = self
            self.children.append(child)

        def __repr__(self):
            return f"Node({self.value})"

    # 创建循环引用
    root = Node("root")
    child1 = Node("child1")
    child2 = Node("child2")

    root.add_child(child1)
    root.add_child(child2)

    # 深拷贝可以处理循环引用
    try:
        root_copy = copy.deepcopy(root)
        print(f"成功深拷贝循环引用结构: {root_copy}")
        print(f"拷贝的子节点: {root_copy.children}")
        print(f"子节点的父节点: {root_copy.children[0].parent}")
    except Exception as e:
        print(f"深拷贝失败: {e}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_assignment_vs_copy()
    demonstrate_shallow_copy()
    demonstrate_deep_copy()
    demonstrate_copy_with_custom_objects()
    demonstrate_copy_pitfalls()

    print("\n=== 演示完成 ===")
    print("理解拷贝机制对避免引用共享问题至关重要！")
