"""
类的基础概念深入 - 基础示例

演示类的基础概念深入的核心概念和使用方法。
"""


def demonstrate_class_creation():
    """演示类的创建和实例化"""
    print("=== 类的创建和实例化演示 ===")
    
    class Person:
        species = "Homo sapiens"  # 类变量
        
        def __init__(self, name, age):
            self.name = name  # 实例变量
            self.age = age
        
        def introduce(self):
            return f"Hi, I'm {self.name}, {self.age} years old"
        
        @classmethod
        def get_species(cls):
            return cls.species
        
        @staticmethod
        def is_adult(age):
            return age >= 18
    
    # 创建实例
    person1 = Person("Alice", 25)
    person2 = Person("Bob", 17)
    
    print(f"Person1: {person1.introduce()}")
    print(f"Person2: {person2.introduce()}")
    print(f"Species: {Person.get_species()}")
    print(f"Alice is adult: {Person.is_adult(person1.age)}")
    print(f"Bob is adult: {Person.is_adult(person2.age)}")
    
    # 检查类和实例的命名空间
    print(f"\nPerson类的__dict__: {Person.__dict__.keys()}")
    print(f"person1实例的__dict__: {person1.__dict__}")


def demonstrate_additional_concepts():
    """演示其他相关概念"""
    print("\n=== 其他概念演示 ===")
    print("这里可以添加更多相关概念的演示")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_class_creation()
    demonstrate_additional_concepts()
    
    print("\n=== 演示完成 ===")
    print("类的基础概念深入是Python编程的重要概念！")
