"""
默认参数陷阱测试

测试默认参数陷阱相关的代码示例和概念。
"""

import pytest
import sys
from io import StringIO

from stage1_python_pitfalls.default_arguments.examples.basic_examples import (
    bad_append,
    good_append,
    demonstrate_basic_concept,
    demonstrate_practical_usage,
    demonstrate_common_patterns,
)


class TestDefaultArguments:
    """测试默认参数陷阱相关功能"""

    def test_mutable_default_is_shared(self):
        a = bad_append(1)
        b = bad_append(2)
        # 同一对象且被两次调用累积
        assert a is b
        assert a == [1, 2]

    def test_none_sentinel_creates_new_list(self):
        a2 = good_append(1)
        b2 = good_append(2)
        # 不同对象，互不影响
        assert a2 is not b2
        assert a2 == [1]
        assert b2 == [2]


class TestDemonstrationFunctions:
    """测试演示函数的输出（不关心文案，仅关心可调用性）"""

    def test_demos_callable(self):
        demonstrate_basic_concept()
        demonstrate_practical_usage()
        demonstrate_common_patterns()


if __name__ == "__main__":
    pytest.main([__file__])
