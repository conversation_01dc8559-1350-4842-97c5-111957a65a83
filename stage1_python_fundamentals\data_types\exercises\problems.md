# 数据类型深入理解 - 练习题

## 基础练习

### 练习1：数据类型特性理解
**题目**：请分析以下代码的输出结果，并解释原因。

```python
# 代码片段1
a = 256
b = 256
print(a is b)

c = 257
d = 257
print(c is d)

# 代码片段2
s1 = "hello"
s2 = "hello"
print(s1 is s2)

s3 = "hello world!"
s4 = "hello world!"
print(s3 is s4)

# 代码片段3
list1 = [1, 2, 3]
list2 = [1, 2, 3]
print(list1 is list2)
print(list1 == list2)
```

**要求**：
- 预测每个print语句的输出
- 解释Python的对象缓存机制
- 说明is和==的区别

**参考答案**：见 `solutions/exercise1_solution.py`

---

### 练习2：内存行为分析
**题目**：编写程序分析不同数据类型的内存使用情况。

**要求**：
- 比较list、tuple、set、dict存储相同数据时的内存占用
- 分析字符串拼接的内存效率
- 测试大整数和浮点数的内存占用差异

**参考答案**：见 `solutions/exercise2_solution.py`

---

### 练习3：类型转换陷阱
**题目**：找出并修复以下代码中的类型转换问题。

```python
# 问题代码
def calculate_average(numbers):
    total = 0
    for num in numbers:
        total += num
    return total / len(numbers)

# 测试数据
test_data = ["10", "20", "30", "40", "50"]
result = calculate_average(test_data)
print(f"平均值: {result}")
```

**要求**：
- 识别类型转换问题
- 提供修复方案
- 添加错误处理

**参考答案**：见 `solutions/exercise3_solution.py`

## 进阶练习

### 练习4：性能优化实战
**题目**：优化以下代码的性能。

```python
# 需要优化的代码
def process_data(data_list):
    result = ""
    numbers = []

    for item in data_list:
        # 字符串拼接
        result += str(item) + ","

        # 数字处理
        if isinstance(item, (int, float)):
            numbers.append(item)

    # 查找操作
    found_items = []
    for target in [1, 100, 1000, 10000]:
        if target in numbers:
            found_items.append(target)

    return result[:-1], found_items  # 去掉最后的逗号

# 测试数据
test_data = list(range(10000)) + ["text"] * 1000
result = process_data(test_data)
```

**要求**：
- 优化字符串拼接
- 优化查找操作
- 测量性能改进效果

**参考答案**：见 `solutions/exercise4_solution.py`

---

### 练习5：自定义数据类型
**题目**：实现一个智能数字类，支持不同精度的计算。

**要求**：
- 自动选择int、float、Decimal类型
- 支持基本数学运算
- 保持最高精度
- 提供类型转换方法

**参考答案**：见 `solutions/exercise5_solution.py`

## 挑战练习

### 练习6：内存池实现
**题目**：实现一个简单的对象内存池。

**要求**：
- 支持对象的创建和回收
- 减少内存分配次数
- 提供使用统计
- 线程安全（可选）

**参考答案**：见 `solutions/exercise6_solution.py`

---

### 练习7：数据类型性能基准测试
**题目**：创建一个全面的数据类型性能测试套件。

**要求**：
- 测试各种容器类型的CRUD操作性能
- 比较不同数据类型的内存效率
- 生成性能报告
- 支持不同数据规模的测试

**参考答案**：见 `solutions/exercise7_solution.py`

## 学习建议

### 练习策略
1. **理论结合实践**：先理解概念，再动手验证
2. **对比分析**：通过对比加深理解
3. **性能意识**：关注代码的性能影响
4. **实际应用**：在项目中应用所学知识

### 验证方法
1. **运行测试**：确保代码正确运行
2. **性能测量**：使用timeit等工具测量性能
3. **内存分析**：使用memory_profiler等工具分析内存
4. **代码审查**：检查代码质量和规范性

### 扩展学习
1. **源码阅读**：研究CPython的实现
2. **性能工具**：学习使用各种性能分析工具
3. **最佳实践**：关注Python性能优化最佳实践
4. **实际项目**：在真实项目中应用优化技巧