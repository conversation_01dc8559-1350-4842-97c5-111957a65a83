# 数据类型深入理解

深入学习Python中各种数据类型的特性、内存表示和使用细节。

## 学习目标

- 理解Python中不同数据类型的内存表示
- 掌握可变类型与不可变类型的区别和影响
- 了解字符串的intern机制和内存优化
- 理解容器类型的内部实现差异
- 掌握数据类型转换的细节和性能考虑

## 核心概念

### 1. 数字类型
- **int**: 任意精度整数，内存动态分配
- **float**: IEEE 754双精度浮点数
- **complex**: 复数类型
- **decimal**: 高精度十进制数
- **fractions**: 分数类型

### 2. 字符串类型
- **str**: Unicode字符串
- **bytes**: 字节序列
- **bytearray**: 可变字节序列
- 字符串intern机制
- 字符串的不可变性

### 3. 容器类型
- **list**: 动态数组，可变
- **tuple**: 不可变序列
- **dict**: 哈希表实现
- **set**: 哈希集合
- **frozenset**: 不可变集合

### 4. 特殊类型
- **None**: 空值类型
- **bool**: 布尔类型（int的子类）
- **type**: 类型对象

## 重要概念

### 可变性 (Mutability)
```python
# 不可变类型
immutable_types = (int, float, str, tuple, frozenset, bool, None)

# 可变类型  
mutable_types = (list, dict, set, bytearray)
```

### 对象身份
```python
# 身份 (identity) - is 操作符
# 类型 (type) - type() 函数
# 值 (value) - == 操作符
```

### 内存管理
- 对象的引用计数
- 垃圾回收机制
- 内存池和对象缓存

## 学习内容

### theory/concepts.md
详细的理论概念解释，包括：
- 数据类型的分类和特性
- 内存管理机制
- 类型系统的设计原理

### examples/basic_examples.py
基础代码示例：
- 各种数据类型的基本使用
- 类型转换示例
- 可变性演示

### examples/advanced_examples.py
进阶代码示例：
- 内存行为分析
- 性能比较
- 底层实现探索

### exercises/problems.md
实践练习题：
- 数据类型选择题
- 内存行为预测题
- 性能优化题

### tests/test_examples.py
单元测试：
- 验证代码示例的正确性
- 测试边界条件
- 性能基准测试

## 学习路径

1. **理论学习** (30分钟)
   - 阅读 `theory/concepts.md`
   - 理解核心概念

2. **基础实践** (45分钟)
   - 运行 `examples/basic_examples.py`
   - 理解每个示例的输出

3. **进阶探索** (60分钟)
   - 分析 `examples/advanced_examples.py`
   - 实验不同的参数和场景

4. **练习巩固** (45分钟)
   - 完成 `exercises/problems.md` 中的题目
   - 对照答案检查理解

5. **测试验证** (30分钟)
   - 运行测试用例
   - 确保所有测试通过

## 关键要点

### 🔍 深入理解
- Python中一切皆对象
- 理解引用和值的区别
- 掌握内存管理机制

### ⚠️ 常见陷阱
- 浮点数精度问题
- 可变对象的共享引用
- 字符串拼接的性能问题

### 💡 最佳实践
- 选择合适的数据类型
- 注意内存使用效率
- 避免不必要的类型转换

## 预期成果

完成本模块学习后，你应该能够：
- [ ] 准确判断数据类型的可变性
- [ ] 解释对象的身份、类型和值的区别
- [ ] 预测代码的内存行为
- [ ] 选择最适合的数据类型
- [ ] 优化数据类型相关的性能问题
