"""
函数机制详解 - 进阶示例

演示Python函数的高级特性：装饰器、生成器、协程等。
"""

import time
import functools
import inspect
from typing import Callable, Any, Generator, Iterator


def demonstrate_function_introspection():
    """演示函数内省机制"""
    print("=== 函数内省演示 ===")

    def sample_function(a: int, b: str = "default", *args, **kwargs) -> str:
        """示例函数用于内省演示"""
        return f"a={a}, b={b}, args={args}, kwargs={kwargs}"

    # 1. 函数基本信息
    print("\n1. 函数基本信息")
    print(f"函数名: {sample_function.__name__}")
    print(f"函数文档: {sample_function.__doc__}")
    print(f"函数模块: {sample_function.__module__}")
    print(f"函数注解: {sample_function.__annotations__}")

    # 2. 使用inspect模块
    print("\n2. 使用inspect模块")
    sig = inspect.signature(sample_function)
    print(f"函数签名: {sig}")

    for name, param in sig.parameters.items():
        print(f"参数 {name}:")
        print(f"  类型: {param.kind}")
        print(f"  默认值: {param.default}")
        print(f"  注解: {param.annotation}")

    # 3. 获取源代码
    print("\n3. 函数源代码")
    try:
        source = inspect.getsource(sample_function)
        print("源代码:")
        print(source)
    except OSError:
        print("无法获取源代码（可能在交互式环境中）")


def demonstrate_advanced_decorators():
    """演示高级装饰器模式"""
    print("\n=== 高级装饰器演示 ===")

    # 1. 带状态的装饰器
    def stateful_decorator(func):
        """带状态的装饰器"""
        func.call_count = 0
        func.call_history = []

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func.call_count += 1
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()

            func.call_history.append({
                'call_number': func.call_count,
                'args': args,
                'kwargs': kwargs,
                'result': result,
                'duration': end_time - start_time
            })

            return result

        return wrapper

    @stateful_decorator
    def fibonacci(n):
        """计算斐波那契数列"""
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)

    print("\n1. 带状态的装饰器")
    result = fibonacci(5)
    print(f"fibonacci(5) = {result}")
    print(f"调用次数: {fibonacci.call_count}")
    print(f"调用历史: {len(fibonacci.call_history)} 次调用")

    # 2. 装饰器工厂
    def retry(max_attempts=3, delay=1):
        """重试装饰器工厂"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                last_exception = None
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        if attempt < max_attempts - 1:
                            print(f"尝试 {attempt + 1} 失败，{delay}秒后重试...")
                            time.sleep(delay)
                        else:
                            print(f"所有 {max_attempts} 次尝试都失败了")
                raise last_exception
            return wrapper
        return decorator

    @retry(max_attempts=3, delay=0.1)
    def unreliable_function():
        """不稳定的函数"""
        import random
        if random.random() < 0.7:  # 70%的失败率
            raise Exception("随机失败")
        return "成功!"

    print("\n2. 装饰器工厂")
    try:
        result = unreliable_function()
        print(f"结果: {result}")
    except Exception as e:
        print(f"最终失败: {e}")


def demonstrate_generator_functions():
    """演示生成器函数的高级用法"""
    print("\n=== 生成器函数演示 ===")

    # 1. 基础生成器
    def countdown(n):
        """倒计时生成器"""
        while n > 0:
            yield n
            n -= 1
        yield "发射!"

    print("\n1. 基础生成器")
    for value in countdown(3):
        print(f"倒计时: {value}")

    # 2. 生成器表达式vs列表推导式
    print("\n2. 内存效率对比")
    import sys

    # 列表推导式
    list_comp = [x**2 for x in range(1000)]
    print(f"列表推导式内存: {sys.getsizeof(list_comp)} bytes")

    # 生成器表达式
    gen_exp = (x**2 for x in range(1000))
    print(f"生成器表达式内存: {sys.getsizeof(gen_exp)} bytes")

    # 3. 协程式生成器（双向通信）
    def echo_generator():
        """回声生成器"""
        value = None
        while True:
            received = yield value
            if received is not None:
                value = f"回声: {received}"
            else:
                value = "等待输入..."

    print("\n3. 协程式生成器")
    echo = echo_generator()
    next(echo)  # 启动生成器

    print(echo.send("Hello"))
    print(echo.send("World"))
    print(echo.send(None))


def demonstrate_function_factories():
    """演示函数工厂模式"""
    print("\n=== 函数工厂演示 ===")

    # 1. 创建配置化的函数
    def make_validator(min_length=0, max_length=100, required_chars=None):
        """创建验证器函数"""
        def validator(text):
            if len(text) < min_length:
                return False, f"长度不能少于{min_length}个字符"
            if len(text) > max_length:
                return False, f"长度不能超过{max_length}个字符"
            if required_chars:
                for char in required_chars:
                    if char not in text:
                        return False, f"必须包含字符'{char}'"
            return True, "验证通过"

        return validator

    print("\n1. 函数工厂创建验证器")

    # 创建不同的验证器
    password_validator = make_validator(min_length=8, required_chars=['@', '#'])
    username_validator = make_validator(min_length=3, max_length=20)

    # 测试验证器
    test_cases = [
        ("password123", password_validator, "密码"),
        ("user@#123", password_validator, "密码"),
        ("ab", username_validator, "用户名"),
        ("validuser", username_validator, "用户名")
    ]

    for text, validator, desc in test_cases:
        is_valid, message = validator(text)
        status = "✓" if is_valid else "✗"
        print(f"{status} {desc} '{text}': {message}")

    # 2. 创建数学运算函数
    def make_operation(operation):
        """创建数学运算函数"""
        operations = {
            'add': lambda x, y: x + y,
            'multiply': lambda x, y: x * y,
            'power': lambda x, y: x ** y,
            'max': lambda x, y: max(x, y),
            'min': lambda x, y: min(x, y)
        }

        if operation not in operations:
            raise ValueError(f"不支持的运算: {operation}")

        base_func = operations[operation]

        def operation_func(*args):
            if len(args) < 2:
                raise ValueError("至少需要两个参数")
            result = args[0]
            for arg in args[1:]:
                result = base_func(result, arg)
            return result

        operation_func.__name__ = f"{operation}_operation"
        operation_func.__doc__ = f"执行{operation}运算"

        return operation_func

    print("\n2. 数学运算函数工厂")

    # 创建运算函数
    add_func = make_operation('add')
    multiply_func = make_operation('multiply')

    print(f"加法: {add_func(1, 2, 3, 4)} = 10")
    print(f"乘法: {multiply_func(2, 3, 4)} = 24")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_function_introspection()
    demonstrate_advanced_decorators()
    demonstrate_generator_functions()
    demonstrate_function_factories()

    print("\n=== 演示完成 ===")
    print("掌握函数的高级特性，能让你写出更优雅、更强大的Python代码！")