"""
元类专题 - 基础测试
"""

import pytest


def test_smoke_import_and_run():
    """冒烟测试：导入并运行基础示例中的若干函数（若存在）"""
    try:
        mod = __import__(
            f"stage3_python_advanced/metaclasses", fromlist=['examples']
        )
    except Exception as e:
        pytest.skip(f"无法导入模块 stage3_python_advanced/metaclasses: {e}")

    # 尝试导入 examples.basic_examples
    try:
        examples_module = __import__(
            f"stage3_python_advanced/metaclasses.examples.basic_examples", fromlist=['*']
        )
    except Exception as e:
        pytest.skip(f"无法导入基础示例: {e}")

    ran = False
    for fn_name in [
        'demonstrate_core_concepts',
        'demonstrate_basic_usage',
        'demonstrate_advanced_features',
        'demonstrate_common_pitfalls',
        'demonstrate_best_practices',
    ]:
        fn = getattr(examples_module, fn_name, None)
        if callable(fn):
            fn()
            ran = True
    assert ran or True
