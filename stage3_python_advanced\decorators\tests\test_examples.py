import pytest
from stage3_python_advanced.decorators.examples.basic_examples import (
    simple_decorator,
    timing_decorator,
    retry_decorator,
    validate_types,
    CountCalls,
    demonstrate_basic_decorator,
    demonstrate_timing_decorator,
    demonstrate_parameterized_decorator,
    demonstrate_type_validation,
    demonstrate_class_decorator,
    demonstrate_multiple_decorators,
)


def test_wraps_preserves_metadata():
    @timing_decorator
    def f():
        """docstring"""
        return 1

    assert f.__name__ == "f"
    assert f.__doc__ == "docstring"


def test_validate_types_works():
    @validate_types(x=int, y=int)
    def add(x, y):
        return x + y

    assert add(1, 2) == 3
    with pytest.raises(TypeError):
        add("1", 2)  # type: ignore


def test_retry_decorator_eventually_passes():
    calls = {"n": 0}

    @retry_decorator(max_attempts=3)
    def flaky():
        calls["n"] += 1
        if calls["n"] < 2:
            raise RuntimeError("fail")
        return "ok"

    assert flaky() == "ok"


def test_class_decorator_counts():
    @CountCalls
    def hello():
        return "hi"

    hello(); hello()
    assert hello.count == 2


def test_demo_functions_callable():
    demonstrate_basic_decorator()
    demonstrate_timing_decorator()
    demonstrate_parameterized_decorator()
    demonstrate_type_validation()
    demonstrate_class_decorator()
    demonstrate_multiple_decorators()

