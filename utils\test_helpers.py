"""
测试辅助工具

提供通用的测试辅助函数和类。
"""

import time
import functools
import sys
from contextlib import contextmanager
from typing import Any, Callable, Dict, List, Optional


def measure_execution_time(func: Callable) -> Callable:
    """装饰器：测量函数执行时间"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        print(f"{func.__name__} 执行时间: {execution_time:.6f} 秒")
        return result
    return wrapper


def compare_performance(func1: Callable, func2: Callable, *args, **kwargs) -> Dict[str, float]:
    """比较两个函数的性能"""
    # 测量第一个函数
    start_time = time.perf_counter()
    result1 = func1(*args, **kwargs)
    time1 = time.perf_counter() - start_time
    
    # 测量第二个函数
    start_time = time.perf_counter()
    result2 = func2(*args, **kwargs)
    time2 = time.perf_counter() - start_time
    
    return {
        'func1_time': time1,
        'func2_time': time2,
        'ratio': time1 / time2 if time2 > 0 else float('inf'),
        'func1_result': result1,
        'func2_result': result2
    }


class MemoryTracker:
    """内存使用跟踪器"""
    
    def __init__(self):
        self.snapshots = []
    
    def take_snapshot(self, label: str = ""):
        """拍摄内存快照"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            self.snapshots.append({
                'label': label,
                'rss': memory_info.rss,  # 物理内存
                'vms': memory_info.vms,  # 虚拟内存
                'timestamp': time.time()
            })
        except ImportError:
            print("警告: psutil未安装，无法跟踪内存使用")
    
    def get_memory_diff(self, start_label: str, end_label: str) -> Optional[Dict[str, int]]:
        """获取两个快照之间的内存差异"""
        start_snapshot = None
        end_snapshot = None
        
        for snapshot in self.snapshots:
            if snapshot['label'] == start_label:
                start_snapshot = snapshot
            elif snapshot['label'] == end_label:
                end_snapshot = snapshot
        
        if start_snapshot and end_snapshot:
            return {
                'rss_diff': end_snapshot['rss'] - start_snapshot['rss'],
                'vms_diff': end_snapshot['vms'] - start_snapshot['vms']
            }
        return None


@contextmanager
def capture_stdout():
    """上下文管理器：捕获标准输出"""
    from io import StringIO
    old_stdout = sys.stdout
    sys.stdout = captured_output = StringIO()
    try:
        yield captured_output
    finally:
        sys.stdout = old_stdout


def create_test_data(data_type: str, size: int = 100) -> Any:
    """创建测试数据"""
    if data_type == 'list':
        return list(range(size))
    elif data_type == 'dict':
        return {f'key_{i}': i for i in range(size)}
    elif data_type == 'set':
        return set(range(size))
    elif data_type == 'tuple':
        return tuple(range(size))
    elif data_type == 'string':
        return ''.join(chr(65 + i % 26) for i in range(size))
    else:
        raise ValueError(f"不支持的数据类型: {data_type}")


class CodeExecutor:
    """代码执行器，用于安全执行和测试代码片段"""
    
    def __init__(self, timeout: float = 5.0):
        self.timeout = timeout
    
    def execute_code(self, code: str, globals_dict: Optional[Dict] = None) -> Dict[str, Any]:
        """执行代码并返回结果"""
        if globals_dict is None:
            globals_dict = {}
        
        locals_dict = {}
        
        try:
            start_time = time.time()
            exec(code, globals_dict, locals_dict)
            execution_time = time.time() - start_time
            
            return {
                'success': True,
                'locals': locals_dict,
                'execution_time': execution_time,
                'error': None
            }
        except Exception as e:
            return {
                'success': False,
                'locals': locals_dict,
                'execution_time': 0,
                'error': str(e)
            }
    
    def evaluate_expression(self, expression: str, globals_dict: Optional[Dict] = None) -> Dict[str, Any]:
        """评估表达式并返回结果"""
        if globals_dict is None:
            globals_dict = {}
        
        try:
            start_time = time.time()
            result = eval(expression, globals_dict)
            execution_time = time.time() - start_time
            
            return {
                'success': True,
                'result': result,
                'execution_time': execution_time,
                'error': None
            }
        except Exception as e:
            return {
                'success': False,
                'result': None,
                'execution_time': 0,
                'error': str(e)
            }


def assert_output_contains(func: Callable, expected_strings: List[str], *args, **kwargs):
    """断言函数输出包含指定字符串"""
    with capture_stdout() as output:
        func(*args, **kwargs)
    
    output_text = output.getvalue()
    for expected in expected_strings:
        assert expected in output_text, f"输出中未找到期望的字符串: '{expected}'"


def benchmark_function(func: Callable, iterations: int = 1000, *args, **kwargs) -> Dict[str, float]:
    """对函数进行基准测试"""
    times = []
    
    for _ in range(iterations):
        start_time = time.perf_counter()
        func(*args, **kwargs)
        end_time = time.perf_counter()
        times.append(end_time - start_time)
    
    return {
        'min_time': min(times),
        'max_time': max(times),
        'avg_time': sum(times) / len(times),
        'total_time': sum(times),
        'iterations': iterations
    }


class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_numbers(count: int = 100, min_val: int = 0, max_val: int = 1000) -> List[int]:
        """生成随机数字列表"""
        import random
        return [random.randint(min_val, max_val) for _ in range(count)]
    
    @staticmethod
    def generate_strings(count: int = 100, length: int = 10) -> List[str]:
        """生成随机字符串列表"""
        import random
        import string
        return [''.join(random.choices(string.ascii_letters, k=length)) for _ in range(count)]
    
    @staticmethod
    def generate_mixed_data(count: int = 100) -> List[Any]:
        """生成混合类型数据列表"""
        import random
        types = [int, str, float, bool]
        data = []
        
        for _ in range(count):
            data_type = random.choice(types)
            if data_type == int:
                data.append(random.randint(0, 1000))
            elif data_type == str:
                data.append(''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=5)))
            elif data_type == float:
                data.append(random.uniform(0, 100))
            elif data_type == bool:
                data.append(random.choice([True, False]))
        
        return data
