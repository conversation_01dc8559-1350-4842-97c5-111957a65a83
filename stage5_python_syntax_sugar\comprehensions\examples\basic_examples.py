"""
推导式专题 - 基础示例

演示推导式专题的核心概念和使用方法。
"""


def demonstrate_comprehensions():
    """演示各种推导式"""
    print("=== 推导式演示 ===")
    
    # 列表推导式
    numbers = range(10)
    squares = [x**2 for x in numbers if x % 2 == 0]
    print(f"偶数的平方: {squares}")
    
    # 字典推导式
    word_lengths = {word: len(word) for word in ['python', 'java', 'go', 'rust']}
    print(f"单词长度: {word_lengths}")
    
    # 集合推导式
    unique_lengths = {len(word) for word in ['hello', 'world', 'python', 'code']}
    print(f"唯一长度: {unique_lengths}")
    
    # 嵌套推导式
    matrix = [[i*j for j in range(1, 4)] for i in range(1, 4)]
    print(f"乘法表矩阵: {matrix}")
    
    # 条件推导式
    filtered_data = [x if x > 0 else 0 for x in [-2, -1, 0, 1, 2]]
    print(f"过滤负数: {filtered_data}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_comprehensions()
    
    print("\n=== 演示完成 ===")
    print("推导式专题让Python代码更加简洁优雅！")
