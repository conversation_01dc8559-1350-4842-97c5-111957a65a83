"""
数据类型深入理解模块

本模块深入探讨Python中各种数据类型的特性、内存表示和使用细节。

主要内容：
- 数字类型的精度和内存表示
- 字符串的intern机制和内存优化
- 容器类型的内部实现差异
- 数据类型转换的细节和性能
"""

from .examples.basic_examples import *
from .examples.advanced_examples import *

__all__ = [
    # 基础示例
    "demonstrate_number_types",
    "demonstrate_string_features", 
    "demonstrate_container_types",
    "demonstrate_type_conversion",
    
    # 进阶示例
    "demonstrate_memory_behavior",
    "demonstrate_string_interning",
    "demonstrate_container_performance",
    "demonstrate_type_system"
]
