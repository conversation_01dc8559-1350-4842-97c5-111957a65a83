"""
Django 高级主题（概念演示）：
- 异步视图与数据库访问注意事项
- Channels / WebSocket 基本概念
"""


def demonstrate_async_views() -> None:
    print("=== 异步视图 ===")
    print("async def my_view(request): return JsonResponse({'ok': True})")
    print("ORM 访问需避开同步阻塞：使用 sync_to_async 包裹或采用异步支持中间层")


def demonstrate_channels_websocket() -> None:
    print("=== Channels / WebSocket ===")
    print("路由：websocket_urlpatterns = [path('ws/chat/', ChatConsumer.as_asgi())]")
    print("消费者：async def connect/receive/disconnect")
    print("层：使用 channel layer 实现跨进程通信")


if __name__ == "__main__":
    demonstrate_async_views()
    demonstrate_channels_websocket()
    print("\n(概念演示，不依赖 Django 实际运行环境)")



