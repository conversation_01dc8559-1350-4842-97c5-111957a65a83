"""
身份vs相等性 - 基础示例

演示身份vs相等性的基本概念和使用方法。
"""

import math


def demonstrate_basic_concept():
    """演示 is 与 == 的区别，以及小整数/字符串驻留等现象"""
    print("=== 身份vs相等性基础演示 ===")

    # 数值
    a = 100
    b = 100
    print(f"小整数缓存: a is b -> {a is b}, a == b -> {a == b}")

    x = 1000
    y = 1000
    print(f"大整数: x is y -> {x is y}, x == y -> {x == y}")

    # 字符串（某些实现会驻留部分字符串）
    s1 = "hello"
    s2 = "hello"
    print(f"字符串驻留(标识符风格): s1 is s2 -> {s1 is s2}, s1 == s2 -> {s1 == s2}")

    s3 = "hello world!"
    s4 = "hello world!"
    print(f"较长字符串: s3 is s4 -> {s3 is s4}, s3 == s4 -> {s3 == s4}")

    # 容器
    l1 = [1, 2, 3]
    l2 = [1, 2, 3]
    print(f"列表值相等但非同一对象: l1 is l2 -> {l1 is l2}, l1 == l2 -> {l1 == l2}")

    l3 = l1
    print(f"引用同一对象: l1 is l3 -> {l1 is l3}, l1 == l3 -> {l1 == l3}")


def demonstrate_practical_usage():
    """演示在判等/判同一对象的实际差异"""
    print("\n=== 实际使用演示 ===")

    sentinel = object()

    def pop_or_default(seq, idx, default=sentinel):
        # 使用 sentinel（唯一对象）来区分“未提供默认值”和“默认值为 None/False/0 等”
        try:
            return seq.pop(idx)
        except Exception:
            return None if default is sentinel else default

    print(pop_or_default([1, 2], 0))
    print(pop_or_default([1, 2], 5))
    print(pop_or_default([1, 2], 5, default=[]))


def demonstrate_common_patterns():
    """常见模式与注意事项"""
    print("\n=== 常见模式演示 ===")

    # NaN 的相等性
    nan = float('nan')
    print(f"NaN 自反性：nan == nan -> {nan == nan}, 使用 math.isnan 检查 -> {math.isnan(nan)}")

    # 字典/集合的相等性与身份
    d1 = {'a': 1}
    d2 = {'a': 1}
    print(f"字典相等但不同对象：d1 is d2 -> {d1 is d2}, d1 == d2 -> {d1 == d2}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_basic_concept()
    demonstrate_practical_usage()
    demonstrate_common_patterns()
    
    print("\n=== 演示完成 ===")
    print("身份vs相等性是Python编程的重要概念！")
