"""
测试模块

测试相关的代码示例和概念。
"""

import pytest
from stage4_python_concurrency.threading_basics.examples.basic_examples import (
    demonstrate_basic_threading,
    demonstrate_thread_with_return_value,
    demonstrate_thread_synchronization,
    demonstrate_producer_consumer,
    demonstrate_thread_local_storage,
    demonstrate_daemon_threads,
    worker,
    calculate_square,
    increment_counter,
    producer,
    consumer,
    process_data,
    background_task,
    main_task,
    increment
)
from stage4_python_concurrency.threading_basics.examples.advanced_examples import (
    demonstrate_advanced_concepts,
    demonstrate_performance_analysis,
    demonstrate_best_practices,
    demonstrate_common_pitfalls,
    demonstrate_real_world_applications
)


class TestBasicExamples:
    """测试基础示例"""
    
    def test_basic_demonstrations_run_without_error(self):
        """测试所有基础演示函数都能正常运行"""
        demonstrate_basic_threading()
        demonstrate_thread_with_return_value()
        demonstrate_thread_synchronization()
        demonstrate_producer_consumer()
        demonstrate_thread_local_storage()
        demonstrate_daemon_threads()
        worker()
        calculate_square()
        increment_counter()
        producer()
        consumer()
        process_data()
        background_task()
        main_task()
        increment()


class TestAdvancedExamples:
    """测试进阶示例"""
    
    def test_advanced_demonstrations_run_without_error(self):
        """测试所有进阶演示函数都能正常运行"""
        demonstrate_advanced_concepts()
        demonstrate_performance_analysis()
        demonstrate_best_practices()
        demonstrate_common_pitfalls()
        demonstrate_real_world_applications()


if __name__ == "__main__":
    pytest.main([__file__])
