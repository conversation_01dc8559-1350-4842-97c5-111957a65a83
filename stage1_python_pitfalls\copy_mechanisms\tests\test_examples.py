"""
测试模块

测试相关的代码示例和概念。
"""

import pytest
from stage1_python_pitfalls.copy_mechanisms.examples.basic_examples import (
    demonstrate_assignment_vs_copy,
    demonstrate_shallow_copy,
    demonstrate_deep_copy,
    demonstrate_copy_with_custom_objects,
    demonstrate_copy_pitfalls,
    add_child
)
from stage1_python_pitfalls.copy_mechanisms.examples.advanced_examples import (
    demonstrate_advanced_concepts,
    demonstrate_performance_analysis,
    demonstrate_best_practices,
    demonstrate_common_pitfalls,
    demonstrate_real_world_applications
)


class TestBasicExamples:
    """测试基础示例"""
    
    def test_basic_demonstrations_run_without_error(self):
        """测试所有基础演示函数都能正常运行"""
        demonstrate_assignment_vs_copy()
        demonstrate_shallow_copy()
        demonstrate_deep_copy()
        demonstrate_copy_with_custom_objects()
        demonstrate_copy_pitfalls()
        add_child()


class TestAdvancedExamples:
    """测试进阶示例"""
    
    def test_advanced_demonstrations_run_without_error(self):
        """测试所有进阶演示函数都能正常运行"""
        demonstrate_advanced_concepts()
        demonstrate_performance_analysis()
        demonstrate_best_practices()
        demonstrate_common_pitfalls()
        demonstrate_real_world_applications()


if __name__ == "__main__":
    pytest.main([__file__])
