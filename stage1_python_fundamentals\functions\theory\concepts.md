# 函数机制详解 - 理论概念

## 1. 函数的本质

在Python中，函数是第一类对象（First-class objects），这意味着函数具有以下特性：

- 可以赋值给变量
- 可以作为参数传递给其他函数
- 可以作为函数的返回值
- 可以存储在数据结构中（如列表、字典）

### 1.1 函数对象

```python
def greet(name):
    return f"Hello, {name}!"

# 函数是对象
print(type(greet))  # <class 'function'>
print(greet.__name__)  # greet

# 可以赋值给变量
say_hello = greet
print(say_hello("Alice"))  # Hello, Alice!
```

### 1.2 函数的属性

每个函数对象都有一些内置属性：

- `__name__`: 函数名
- `__doc__`: 文档字符串
- `__module__`: 函数所在模块
- `__code__`: 代码对象
- `__defaults__`: 默认参数值
- `__annotations__`: 类型注解

## 2. 参数传递机制

### 2.1 Python的参数传递方式

Python使用"传对象引用"（pass-by-object-reference）的方式：

- 传递的是对象的引用，而不是对象的副本
- 对于不可变对象，表现类似"传值"
- 对于可变对象，表现类似"传引用"

```python
def modify_immutable(x):
    x = x + 1  # 创建新对象
    return x

def modify_mutable(lst):
    lst.append(4)  # 修改原对象
    return lst

# 不可变对象
num = 5
result = modify_immutable(num)
print(num)     # 5 (未改变)
print(result)  # 6

# 可变对象
my_list = [1, 2, 3]
result = modify_mutable(my_list)
print(my_list)  # [1, 2, 3, 4] (已改变)
print(result)   # [1, 2, 3, 4]
```

### 2.2 参数类型

Python支持多种参数类型：

1. **位置参数**（Positional Arguments）
2. **关键字参数**（Keyword Arguments）
3. **默认参数**（Default Arguments）
4. **可变位置参数**（*args）
5. **可变关键字参数**（**kwargs）
6. **仅限位置参数**（Positional-only，Python 3.8+）
7. **仅限关键字参数**（Keyword-only）

```python
def complex_function(pos_only, /, pos_or_kw, *, kw_only, **kwargs):
    """
    pos_only: 仅限位置参数
    pos_or_kw: 位置或关键字参数
    kw_only: 仅限关键字参数
    **kwargs: 可变关键字参数
    """
    pass
```

## 3. 作用域和命名空间

### 3.1 LEGB规则

Python使用LEGB规则来解析变量名：

- **L**ocal: 局部作用域
- **E**nclosing: 嵌套作用域
- **G**lobal: 全局作用域
- **B**uilt-in: 内置作用域

```python
x = "global"  # 全局作用域

def outer():
    x = "enclosing"  # 嵌套作用域

    def inner():
        x = "local"  # 局部作用域
        print(x)  # local

    inner()
    print(x)  # enclosing

outer()
print(x)  # global
```

### 3.2 global和nonlocal关键字

```python
x = "global"

def modify_global():
    global x
    x = "modified global"

def outer():
    x = "enclosing"

    def modify_enclosing():
        nonlocal x
        x = "modified enclosing"

    modify_enclosing()
    print(x)  # modified enclosing

modify_global()
print(x)  # modified global
```

## 4. 闭包（Closures）

### 4.1 闭包的定义

闭包是指：
- 嵌套函数
- 内部函数引用外部函数的变量
- 外部函数返回内部函数

```python
def make_multiplier(factor):
    def multiplier(number):
        return number * factor  # 引用外部变量
    return multiplier

# 创建闭包
double = make_multiplier(2)
triple = make_multiplier(3)

print(double(5))  # 10
print(triple(5))  # 15

# 检查闭包
print(double.__closure__)  # 包含factor的值
```

### 4.2 闭包的应用

闭包常用于：
- 装饰器
- 回调函数
- 事件处理
- 函数工厂

## 5. 递归

### 5.1 递归的概念

递归是函数调用自身的编程技术，包含：
- **基础情况**：递归终止条件
- **递归情况**：函数调用自身

```python
def factorial(n):
    # 基础情况
    if n <= 1:
        return 1
    # 递归情况
    return n * factorial(n - 1)

print(factorial(5))  # 120
```

### 5.2 递归的优化

Python有递归深度限制，可以通过以下方式优化：

```python
import sys

# 查看递归限制
print(sys.getrecursionlimit())  # 默认1000

# 设置递归限制
sys.setrecursionlimit(2000)

# 尾递归优化（Python不支持，但可以手动实现）
def factorial_tail(n, acc=1):
    if n <= 1:
        return acc
    return factorial_tail(n - 1, n * acc)
```

## 6. 高阶函数

### 6.1 函数作为参数

```python
def apply_operation(numbers, operation):
    return [operation(x) for x in numbers]

def square(x):
    return x ** 2

def cube(x):
    return x ** 3

numbers = [1, 2, 3, 4, 5]
print(apply_operation(numbers, square))  # [1, 4, 9, 16, 25]
print(apply_operation(numbers, cube))    # [1, 8, 27, 64, 125]
```

### 6.2 函数作为返回值

```python
def make_validator(min_length):
    def validator(text):
        return len(text) >= min_length
    return validator

# 创建不同的验证器
password_validator = make_validator(8)
username_validator = make_validator(3)

print(password_validator("12345"))     # False
print(password_validator("12345678"))  # True
print(username_validator("abc"))       # True
```

## 7. 内置高阶函数

### 7.1 map()函数

```python
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x**2, numbers))
print(squared)  # [1, 4, 9, 16, 25]
```

### 7.2 filter()函数

```python
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
evens = list(filter(lambda x: x % 2 == 0, numbers))
print(evens)  # [2, 4, 6, 8, 10]
```

### 7.3 reduce()函数

```python
from functools import reduce

numbers = [1, 2, 3, 4, 5]
sum_all = reduce(lambda x, y: x + y, numbers)
print(sum_all)  # 15
```

## 8. Lambda函数

### 8.1 Lambda语法

```python
# 普通函数
def add(x, y):
    return x + y

# Lambda函数
add_lambda = lambda x, y: x + y

print(add(3, 5))        # 8
print(add_lambda(3, 5)) # 8
```

### 8.2 Lambda的使用场景

Lambda适用于：
- 简单的单行函数
- 作为高阶函数的参数
- 临时使用的函数

```python
# 排序
students = [('Alice', 85), ('Bob', 90), ('Charlie', 78)]
students.sort(key=lambda x: x[1])  # 按成绩排序
print(students)  # [('Charlie', 78), ('Alice', 85), ('Bob', 90)]
```

## 9. 函数注解

### 9.1 类型注解

```python
def greet(name: str, age: int) -> str:
    return f"Hello, {name}! You are {age} years old."

# 查看注解
print(greet.__annotations__)
# {'name': <class 'str'>, 'age': <class 'int'>, 'return': <class 'str'>}
```

### 9.2 复杂类型注解

```python
from typing import List, Dict, Optional, Union

def process_data(
    items: List[str],
    config: Dict[str, Union[str, int]],
    debug: Optional[bool] = None
) -> List[Dict[str, str]]:
    """处理数据的函数"""
    pass
```

## 10. 性能考虑

### 10.1 函数调用开销

```python
import time

def simple_function():
    return 1

# 测量函数调用开销
start = time.perf_counter()
for _ in range(1000000):
    simple_function()
end = time.perf_counter()
print(f"函数调用开销: {end - start:.6f} 秒")
```

### 10.2 优化技巧

1. **避免不必要的函数调用**
2. **使用局部变量**
3. **缓存计算结果**
4. **使用内置函数**

```python
# 优化前
def slow_function(items):
    result = []
    for item in items:
        if len(item) > 5:  # 重复调用len()
            result.append(item.upper())  # 重复调用upper()
    return result

# 优化后
def fast_function(items):
    result = []
    append = result.append  # 缓存方法
    for item in items:
        item_len = len(item)  # 缓存长度
        if item_len > 5:
            append(item.upper())
    return result
```

## 总结

理解函数机制对于编写高质量Python代码至关重要：

1. **函数是对象**：可以像其他对象一样操作
2. **参数传递**：理解引用传递的机制
3. **作用域规则**：掌握LEGB规则
4. **闭包应用**：理解闭包的创建和使用
5. **递归技巧**：合理使用递归
6. **高阶函数**：函数式编程的基础
7. **性能优化**：避免不必要的开销