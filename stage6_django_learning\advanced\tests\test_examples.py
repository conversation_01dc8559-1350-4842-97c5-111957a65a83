"""
Django 进阶示例的烟雾测试（不依赖真实 Django 环境）。
"""

from stage6_django_learning.advanced.examples.basic_examples import (
    demonstrate_caching_layers,
    demonstrate_auth_permissions,
)
from stage6_django_learning.advanced.examples.advanced_examples import (
    demonstrate_async_views,
    demonstrate_channels_websocket,
)


def test_advanced_examples_smoke(capsys):
    demonstrate_caching_layers()
    demonstrate_auth_permissions()
    demonstrate_async_views()
    demonstrate_channels_websocket()
    out, err = capsys.readouterr()
    assert "缓存层" in out
    assert "认证与权限" in out
    assert "异步视图" in out
    assert "WebSocket" in out



