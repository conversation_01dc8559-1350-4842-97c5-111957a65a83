"""
多进程编程 - 基础示例

演示多进程编程的核心概念和使用方法。
"""


def demonstrate_multiprocessing():
    """演示多进程编程"""
    print("=== 多进程编程演示 ===")
    
    import multiprocessing
    import time
    import os
    
    def worker_function(name, duration):
        """工作进程函数"""
        print(f"进程 {name} (PID: {os.getpid()}) 开始工作")
        time.sleep(duration)
        print(f"进程 {name} (PID: {os.getpid()}) 完成工作")
        return f"进程 {name} 的结果"
    
    def cpu_bound_task(n):
        """CPU密集型任务"""
        result = 0
        for i in range(n):
            result += i * i
        return result
    
    # 创建进程
    print("1. 创建单个进程")
    process = multiprocessing.Process(
        target=worker_function, 
        args=("Worker-1", 2)
    )
    process.start()
    process.join()
    
    # 使用进程池
    print("\n2. 使用进程池")
    with multiprocessing.Pool(processes=3) as pool:
        tasks = [1000000, 2000000, 3000000]
        results = pool.map(cpu_bound_task, tasks)
        print(f"CPU密集型任务结果: {results}")
    
    print(f"\n主进程 PID: {os.getpid()}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_multiprocessing()
    
    print("\n=== 演示完成 ===")
    print("多进程编程是Python并发编程的重要技术！")
