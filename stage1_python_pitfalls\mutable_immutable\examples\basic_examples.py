"""
可变vs不可变对象 - 基础示例

演示Python中可变和不可变对象的区别及其影响。
"""


def demonstrate_immutable_objects():
    """演示不可变对象的特性"""
    print("=== 不可变对象演示 ===")

    # 整数
    print("\n1. 整数 (int)")
    a = 10
    a_id = id(a)
    print(f"a = {a}, id = {a_id}")
    a += 5  # 创建新对象
    print(f"a += 5 后: a = {a}, id = {id(a)}")
    print(f"对象是否改变: {id(a) != a_id}")

    # 字符串
    print("\n2. 字符串 (str)")
    s = "hello"
    s_id = id(s)
    print(f"s = '{s}', id = {s_id}")
    s += " world"  # 创建新对象
    print(f"s += ' world' 后: s = '{s}', id = {id(s)}")
    print(f"对象是否改变: {id(s) != s_id}")

    # 元组
    print("\n3. 元组 (tuple)")
    t = (1, 2, 3)
    t_id = id(t)
    print(f"t = {t}, id = {t_id}")
    t = t + (4, 5)  # 创建新对象
    print(f"t + (4, 5) 后: t = {t}, id = {id(t)}")
    print(f"对象是否改变: {id(t) != t_id}")


def demonstrate_mutable_objects():
    """演示可变对象的特性"""
    print("\n=== 可变对象演示 ===")

    # 列表
    print("\n1. 列表 (list)")
    lst = [1, 2, 3]
    lst_id = id(lst)
    print(f"lst = {lst}, id = {lst_id}")
    lst.append(4)  # 修改原对象
    print(f"lst.append(4) 后: lst = {lst}, id = {id(lst)}")
    print(f"对象是否改变: {id(lst) != lst_id}")

    # 字典
    print("\n2. 字典 (dict)")
    d = {'a': 1, 'b': 2}
    d_id = id(d)
    print(f"d = {d}, id = {d_id}")
    d['c'] = 3  # 修改原对象
    print(f"d['c'] = 3 后: d = {d}, id = {id(d)}")
    print(f"对象是否改变: {id(d) != d_id}")

    # 集合
    print("\n3. 集合 (set)")
    s = {1, 2, 3}
    s_id = id(s)
    print(f"s = {s}, id = {s_id}")
    s.add(4)  # 修改原对象
    print(f"s.add(4) 后: s = {s}, id = {id(s)}")
    print(f"对象是否改变: {id(s) != s_id}")


def demonstrate_function_parameter_passing():
    """演示函数参数传递中的可变性影响"""
    print("\n=== 函数参数传递演示 ===")

    def modify_immutable(x):
        """尝试修改不可变对象"""
        print(f"函数内部修改前: x = {x}, id = {id(x)}")
        x += 10
        print(f"函数内部修改后: x = {x}, id = {id(x)}")
        return x

    def modify_mutable(lst):
        """修改可变对象"""
        print(f"函数内部修改前: lst = {lst}, id = {id(lst)}")
        lst.append(4)
        print(f"函数内部修改后: lst = {lst}, id = {id(lst)}")
        return lst

    # 不可变对象参数
    print("\n1. 传递不可变对象")
    num = 5
    print(f"调用前: num = {num}, id = {id(num)}")
    result = modify_immutable(num)
    print(f"调用后: num = {num}, id = {id(num)}")
    print(f"返回值: result = {result}")
    print("结论: 原对象未被修改")

    # 可变对象参数
    print("\n2. 传递可变对象")
    my_list = [1, 2, 3]
    print(f"调用前: my_list = {my_list}, id = {id(my_list)}")
    result = modify_mutable(my_list)
    print(f"调用后: my_list = {my_list}, id = {id(my_list)}")
    print(f"返回值: result = {result}")
    print("结论: 原对象被修改了")


def demonstrate_assignment_behavior():
    """演示赋值行为的差异"""
    print("\n=== 赋值行为演示 ===")

    # 不可变对象的赋值
    print("\n1. 不可变对象赋值")
    a = 10
    b = a
    print(f"初始: a = {a}, b = {b}")
    print(f"a is b: {a is b}")

    a += 5  # a指向新对象
    print(f"a += 5 后: a = {a}, b = {b}")
    print(f"a is b: {a is b}")
    print("结论: 修改a不影响b")

    # 可变对象的赋值
    print("\n2. 可变对象赋值")
    list1 = [1, 2, 3]
    list2 = list1  # 两个变量指向同一个对象
    print(f"初始: list1 = {list1}, list2 = {list2}")
    print(f"list1 is list2: {list1 is list2}")

    list1.append(4)  # 修改共享的对象
    print(f"list1.append(4) 后: list1 = {list1}, list2 = {list2}")
    print(f"list1 is list2: {list1 is list2}")
    print("结论: 修改list1也影响了list2")


def demonstrate_common_pitfalls():
    """演示常见的陷阱"""
    print("\n=== 常见陷阱演示 ===")

    # 陷阱1: 列表乘法
    print("\n1. 列表乘法陷阱")
    matrix = [[0] * 3] * 3  # 错误的方式
    print(f"matrix = {matrix}")
    matrix[0][0] = 1
    print(f"修改matrix[0][0] = 1 后: {matrix}")
    print("问题: 所有行都是同一个列表对象")

    # 正确的方式
    correct_matrix = [[0] * 3 for _ in range(3)]
    print(f"正确的matrix = {correct_matrix}")
    correct_matrix[0][0] = 1
    print(f"修改后: {correct_matrix}")

    # 陷阱2: 默认参数
    print("\n2. 默认参数陷阱")
    def append_to_list(item, target_list=[]):  # 危险的默认参数
        target_list.append(item)
        return target_list

    result1 = append_to_list(1)
    result2 = append_to_list(2)
    print(f"第一次调用: {result1}")
    print(f"第二次调用: {result2}")
    print("问题: 默认参数在所有调用间共享")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_immutable_objects()
    demonstrate_mutable_objects()
    demonstrate_function_parameter_passing()
    demonstrate_assignment_behavior()
    demonstrate_common_pitfalls()

    print("\n=== 演示完成 ===")
    print("理解可变性是Python编程的关键概念！")