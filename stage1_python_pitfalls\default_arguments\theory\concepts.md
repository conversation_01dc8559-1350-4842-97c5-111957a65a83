# 默认参数陷阱 - 理论概念

## 1. 概述

默认参数陷阱是Python编程中的重要概念，理解它对于编写高质量的代码至关重要。

### 1.1 定义

“默认参数陷阱”指的是在函数定义时使用“可变对象”作为默认参数（如 list/dict/set），导致多次调用之间共享同一对象，产生意外的状态污染。

### 1.2 重要性
- 避免难以排查的“数据被意外累积或篡改”问题
- 提升代码可维护性与可预测性

## 2. 核心概念

### 2.1 基础概念
- 默认参数在函数定义阶段（而非调用阶段）求值，仅求值一次
- 对于可变对象，后续调用若修改该对象，所有调用方共享修改结果
- 使用 None 作为“哨兵值”（sentinel），在函数体内部创建新对象是惯用法

### 2.2 工作原理
- Python 在函数定义时将默认参数绑定到函数对象的 __defaults__（或 __kwdefaults__）
- 每次调用若没有提供该参数，就直接复用绑定的同一个对象引用

## 3. 实际应用

### 3.1 使用场景
- 累计数据（应避免使用可变默认参数）
- 配置参数（建议使用不可变对象或 None + 体内构造）
- 缓存/记忆化（建议用 lru_cache、闭包或类属性等更明确的机制）

### 3.2 最佳实践
- 对于列表/字典/集合等可变对象，默认参数应为 None，在函数体内初始化
- 若必须复用外部对象，显式复制（list(x)/dict(x)/copy/deepcopy）后再修改
- 在代码评审中关注函数默认参数的类型，优先用不可变对象作为默认值

## 4. 常见陷阱

### 4.1 常见错误
- 在函数定义中写 def f(x, bucket=[]): 并在函数内 append，导致所有调用共享 bucket
- 使用 dict/set 作为默认值后修改，跨调用共享污染

### 4.2 避免方法
- def f(x, bucket=None): if bucket is None: bucket = []
- 明确文档注释，说明默认参数行为与“是否会修改入参”

## 5. 性能考虑

### 5.1 性能影响
- None + 体内创建引入微小开销，但换来正确性与可维护性
- 极少数对性能极端敏感的路径，才考虑缓存对象，但需文档清晰且测试覆盖

### 5.2 优化建议
- 尽量使用不可变默认值（如 ()/frozenset/str/int/None 等）
- 对必须复用的对象进行只读封装或 copy-on-write 策略

## 总结
- 默认参数在定义时求值，仅一次
- 可变默认参数会跨调用共享，容易踩坑
- 正确用法：None 作为哨兵，函数体内创建新对象
- 需要复用时，采用显式复制或其他更明确的缓存机制
