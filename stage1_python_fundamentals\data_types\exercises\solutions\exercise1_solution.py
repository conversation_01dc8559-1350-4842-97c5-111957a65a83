"""
练习1解答：数据类型特性理解

分析Python的对象缓存机制和身份比较。
"""


def analyze_integer_caching():
    """分析整数缓存机制"""
    print("=== 整数缓存机制分析 ===")
    
    # 代码片段1分析
    print("\n1. 小整数缓存 (-5 到 256)")
    a = 256
    b = 256
    result1 = a is b
    print(f"a = 256, b = 256")
    print(f"a is b = {result1}")
    print(f"解释: Python缓存-5到256之间的小整数，所以a和b指向同一个对象")
    
    print("\n2. 大整数不缓存")
    c = 257
    d = 257
    result2 = c is d
    print(f"c = 257, d = 257")
    print(f"c is d = {result2}")
    print(f"解释: 257超出缓存范围，每次创建新对象，所以c和d是不同对象")
    
    # 验证对象ID
    print(f"\nid(a) = {id(a)}, id(b) = {id(b)}")
    print(f"id(c) = {id(c)}, id(d) = {id(d)}")
    
    return result1, result2


def analyze_string_interning():
    """分析字符串驻留机制"""
    print("\n=== 字符串驻留机制分析 ===")
    
    # 代码片段2分析
    print("\n1. 标识符样式字符串")
    s1 = "hello"
    s2 = "hello"
    result1 = s1 is s2
    print(f"s1 = 'hello', s2 = 'hello'")
    print(f"s1 is s2 = {result1}")
    print(f"解释: 'hello'是标识符样式，会被自动驻留")
    
    print("\n2. 包含特殊字符的字符串")
    s3 = "hello world!"
    s4 = "hello world!"
    result2 = s3 is s4
    print(f"s3 = 'hello world!', s4 = 'hello world!'")
    print(f"s3 is s4 = {result2}")
    print(f"解释: 包含空格和感叹号，在某些Python版本中可能不会自动驻留")
    print(f"但在编译时确定的字符串字面量通常会被驻留")
    
    # 验证对象ID
    print(f"\nid(s1) = {id(s1)}, id(s2) = {id(s2)}")
    print(f"id(s3) = {id(s3)}, id(s4) = {id(s4)}")
    
    return result1, result2


def analyze_list_identity():
    """分析列表身份和相等性"""
    print("\n=== 列表身份vs相等性分析 ===")
    
    # 代码片段3分析
    list1 = [1, 2, 3]
    list2 = [1, 2, 3]
    
    identity_result = list1 is list2
    equality_result = list1 == list2
    
    print(f"list1 = [1, 2, 3], list2 = [1, 2, 3]")
    print(f"list1 is list2 = {identity_result}")
    print(f"list1 == list2 = {equality_result}")
    
    print(f"\n解释:")
    print(f"- is 比较对象身份（内存地址），list1和list2是不同的对象")
    print(f"- == 比较对象值，两个列表包含相同的元素")
    print(f"- 可变对象（如list）不会被缓存，每次创建新对象")
    
    # 验证对象ID
    print(f"\nid(list1) = {id(list1)}, id(list2) = {id(list2)}")
    
    return identity_result, equality_result


def demonstrate_is_vs_equals():
    """演示is和==的区别"""
    print("\n=== is vs == 详细对比 ===")
    
    examples = [
        # (描述, 对象1, 对象2)
        ("小整数", 100, 100),
        ("大整数", 1000, 1000),
        ("字符串", "hello", "hello"),
        ("列表", [1, 2], [1, 2]),
        ("元组", (1, 2), (1, 2)),
        ("None", None, None),
        ("布尔值", True, True),
    ]
    
    print(f"{'类型':<10} {'is结果':<8} {'==结果':<8} {'说明'}")
    print("-" * 50)
    
    for desc, obj1, obj2 in examples:
        is_result = obj1 is obj2
        eq_result = obj1 == obj2
        
        if is_result and eq_result:
            explanation = "身份相同，值相等"
        elif not is_result and eq_result:
            explanation = "身份不同，值相等"
        elif is_result and not eq_result:
            explanation = "身份相同，值不等（罕见）"
        else:
            explanation = "身份不同，值不等"
        
        print(f"{desc:<10} {is_result!s:<8} {eq_result!s:<8} {explanation}")


def main():
    """主函数：运行所有分析"""
    print("数据类型特性理解 - 练习1解答")
    print("=" * 50)
    
    # 预测结果
    print("预测结果:")
    print("a is b (256): True  - 小整数缓存")
    print("c is d (257): False - 大整数不缓存")
    print("s1 is s2 ('hello'): True  - 字符串驻留")
    print("s3 is s4 ('hello world!'): True  - 字符串字面量驻留")
    print("list1 is list2: False - 可变对象不缓存")
    print("list1 == list2: True  - 值相等")
    
    print("\n" + "=" * 50)
    
    # 实际验证
    int_results = analyze_integer_caching()
    str_results = analyze_string_interning()
    list_results = analyze_list_identity()
    
    demonstrate_is_vs_equals()
    
    # 总结
    print("\n=== 总结 ===")
    print("1. Python缓存小整数(-5到256)和某些字符串")
    print("2. is 比较对象身份，== 比较对象值")
    print("3. 可变对象（list, dict, set）不会被缓存")
    print("4. 不可变对象（int, str, tuple）可能被缓存或驻留")
    print("5. 理解这些机制有助于避免常见陷阱和优化性能")


if __name__ == "__main__":
    main()
