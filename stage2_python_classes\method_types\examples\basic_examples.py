"""
方法类型详解 - 基础示例

演示方法类型详解的核心概念和使用方法。
"""


def demonstrate_method_types():
    """演示不同类型的方法"""
    print("=== 方法类型演示 ===")
    
    class Calculator:
        pi = 3.14159
        
        def __init__(self, name):
            self.name = name
        
        def add(self, a, b):  # 实例方法
            """实例方法，可以访问实例和类属性"""
            print(f"{self.name} 计算: {a} + {b} = {a + b}")
            return a + b
        
        @classmethod
        def circle_area(cls, radius):  # 类方法
            """类方法，可以访问类属性，不能访问实例属性"""
            area = cls.pi * radius ** 2
            print(f"圆的面积: π × {radius}² = {area}")
            return area
        
        @staticmethod
        def multiply(a, b):  # 静态方法
            """静态方法，不能访问类或实例属性"""
            result = a * b
            print(f"静态计算: {a} × {b} = {result}")
            return result
    
    calc = Calculator("我的计算器")
    
    # 调用实例方法
    calc.add(5, 3)
    
    # 调用类方法
    Calculator.circle_area(5)
    calc.circle_area(3)  # 也可以通过实例调用
    
    # 调用静态方法
    Calculator.multiply(4, 6)
    calc.multiply(2, 8)  # 也可以通过实例调用


def demonstrate_additional_concepts():
    """演示其他相关概念"""
    print("\n=== 其他概念演示 ===")
    print("这里可以添加更多相关概念的演示")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_method_types()
    demonstrate_additional_concepts()
    
    print("\n=== 演示完成 ===")
    print("方法类型详解是Python编程的重要概念！")
