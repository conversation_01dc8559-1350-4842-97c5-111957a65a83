"""
Django 进阶示例（概念演示）：
- 缓存层（per-view / per-site / 低层 API）
- 认证与权限（装饰器与 mixin）
"""


def demonstrate_caching_layers() -> None:
    print("=== 缓存层 ===")
    print("1) 视图缓存：@cache_page(60 * 5)")
    print("2) 全站缓存：MIDDLEWARE + 缓存后端配置")
    print("3) 低层 API：cache.set('k','v',timeout=60)")


def demonstrate_auth_permissions() -> None:
    print("=== 认证与权限 ===")
    print("函数视图：@login_required")
    print("类视图：LoginRequiredMixin / PermissionRequiredMixin")
    print("DRF：IsAuthenticated / IsAdminUser / 自定义权限")


if __name__ == "__main__":
    demonstrate_caching_layers()
    demonstrate_auth_permissions()
    print("\n(概念演示，不依赖 Django 实际运行环境)")



