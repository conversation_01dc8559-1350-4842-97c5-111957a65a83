"""
生成器专题 - 基础示例

演示生成器专题的核心概念和使用方法。
"""


def demonstrate_generators():
    """演示生成器的使用"""
    print("=== 生成器演示 ===")
    
    def fibonacci_generator(n):
        """斐波那契数列生成器"""
        a, b = 0, 1
        count = 0
        while count < n:
            yield a
            a, b = b, a + b
            count += 1
    
    # 使用生成器
    print("斐波那契数列前10项:")
    for num in fibonacci_generator(10):
        print(num, end=" ")
    print()
    
    # 生成器表达式
    squares = (x**2 for x in range(5))
    print(f"平方数: {list(squares)}")
    
    # 生成器的内存优势
    import sys
    list_comp = [x for x in range(1000)]
    gen_exp = (x for x in range(1000))
    
    print(f"\n列表推导式内存占用: {sys.getsizeof(list_comp)} bytes")
    print(f"生成器表达式内存占用: {sys.getsizeof(gen_exp)} bytes")


def demonstrate_additional_concepts():
    """演示其他相关概念"""
    print("\n=== 其他概念演示 ===")
    print("这里可以添加更多相关概念的演示")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_generators()
    demonstrate_additional_concepts()
    
    print("\n=== 演示完成 ===")
    print("生成器专题是Python编程的重要概念！")
