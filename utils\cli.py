"""
命令行入口：`python-learning`

提供一些便捷命令运行示例或测试。
"""

import argparse
import subprocess
import sys
from pathlib import Path
from typing import List


def _run_command(command: List[str]) -> int:
    process = subprocess.Popen(command)
    return process.wait()


def main() -> int:
    parser = argparse.ArgumentParser(prog="python-learning", description="Python 进阶学习项目 CLI")
    subparsers = parser.add_subparsers(dest="command")

    examples = subparsers.add_parser("examples", help="运行某个示例")
    examples.add_argument("path", help="示例相对路径，如 stage1_python_fundamentals/data_types/examples/basic_examples.py")

    tests = subparsers.add_parser("tests", help="运行测试")

    fill = subparsers.add_parser("fill", help="完善空/占位内容（默认不生成 advanced 示例）")
    fill.add_argument("--dry-run", action="store_true", help="仅打印将要变更的文件，不写入")
    fill.add_argument("--backup", action="store_true", help="写入前备份原文件为 .bak")
    fill.add_argument("--stage", dest="stage_filter", help="仅处理指定 stage，如 stage3_python_advanced")
    fill.add_argument("--module", dest="module_filter", help="仅处理指定模块路径，如 stage1_python_pitfalls/default_arguments")
    fill.add_argument("--aggressive", action="store_true", help="激进模式：放宽占位识别，尽量覆盖占位文件")

    tests.add_argument("target", nargs="?", default=".", help="测试目标路径，默认当前目录")

    args = parser.parse_args()
    project_root = Path(__file__).resolve().parents[1]
    cwd = str(project_root)
    # 确保可导入项目根目录下的模块
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    if args.command == "examples":
        script_path = project_root / args.path
        if not script_path.exists():
            print(f"找不到示例: {script_path}")
            return 1
        return _run_command([sys.executable, str(script_path)])

    if args.command == "tests":
        return _run_command([sys.executable, "-m", "pytest", args.target])

    if args.command == "fill":
        # 延迟导入，避免 CLI 引入时执行生成逻辑
        from complete_all_content import fill_all_empty_content
        fill_all_empty_content(
            dry_run=args.dry_run,
            backup=args.backup,
            stage_filter=args.stage_filter,
            module_filter=args.module_filter,
            aggressive=args.aggressive,
        )
        return 0

    parser.print_help()
    return 0


if __name__ == "__main__":
    raise SystemExit(main())


