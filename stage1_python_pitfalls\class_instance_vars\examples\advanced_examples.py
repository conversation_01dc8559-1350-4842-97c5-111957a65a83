"""
类变量 vs 实例变量的高级示例：
- 可变类变量引发的共享状态问题
- 使用 dataclass/默认工厂避免陷阱
"""

from dataclasses import dataclass, field
from typing import List


class BadCounter:
    # 错误：可变类变量，所有实例共享
    history: List[int] = []

    def __init__(self, start: int = 0) -> None:
        self.value = start

    def inc(self, step: int = 1) -> None:
        self.value += step
        # 追加到类变量，意外共享
        self.history.append(self.value)


@dataclass
class GoodCounter:
    value: int = 0
    # 正确：每个实例一个独立列表
    history: List[int] = field(default_factory=list)

    def inc(self, step: int = 1) -> None:
        self.value += step
        self.history.append(self.value)


def demonstrate_class_instance_var_pitfall() -> None:
    print("=== 类变量 vs 实例变量（高级）===")

    a = BadCounter()
    b = BadCounter()
    a.inc(); a.inc()
    b.inc()
    print(f"BadCounter 历史 A: {a.history}")
    print(f"BadCounter 历史 B: {b.history} (共享，非预期)")

    x = GoodCounter()
    y = GoodCounter()
    x.inc(); x.inc()
    y.inc()
    print(f"GoodCounter 历史 X: {x.history}")
    print(f"GoodCounter 历史 Y: {y.history} (独立，符合预期)")


if __name__ == "__main__":
    demonstrate_class_instance_var_pitfall()



