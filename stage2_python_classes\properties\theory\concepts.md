# 属性管理（property）- 理论概念

## 1. 定义与动机
- property 提供“属性式访问”的接口，背后可绑定 getter/setter/deleter
- 适合：输入验证、惰性计算、只读视图、与外部表示解耦

## 2. 基本语法
```python
class C:
    def __init__(self):
        self._x = 0

    @property
    def x(self):
        return self._x

    @x.setter
    def x(self, value):
        if value < 0:
            raise ValueError
        self._x = value
```

## 3. 最佳实践
- 使用前导下划线 _x 存储内部状态
- 在 setter 中进行类型/范围校验
- 只读属性只定义 @property，不定义 setter
- 需要缓存时可配合 @functools.cached_property（Python3.8+）

## 4. 反模式与陷阱
- 过度包装简单字段，增加复杂度
- setter 中做重活导致隐藏副作用

## 5. 与描述符/数据模型的关系
- property 是描述符（descriptor）的一种语法糖
- 更复杂的复用场景可定义自定义描述符类

