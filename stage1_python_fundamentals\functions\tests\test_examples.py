"""
函数机制详解测试

测试函数相关的代码示例和概念。
"""

import pytest
from stage1_python_fundamentals.functions.examples.basic_examples import (
    demonstrate_function_as_object,
    demonstrate_parameter_passing,
    demonstrate_parameter_types,
    demonstrate_scope_and_legb,
    demonstrate_closures,
    demonstrate_recursion,
    demonstrate_higher_order_functions
)
from stage1_python_fundamentals.functions.examples.advanced_examples import (
    demonstrate_function_introspection,
    demonstrate_advanced_decorators,
    demonstrate_generator_functions,
    demonstrate_function_factories
)


class TestBasicExamples:
    """测试基础示例"""

    def test_function_as_object_demonstration(self, capsys):
        """测试函数作为对象演示"""
        demonstrate_function_as_object()
        captured = capsys.readouterr()
        assert "函数作为对象演示" in captured.out
        assert "函数名" in captured.out

    def test_parameter_passing_demonstration(self, capsys):
        """测试参数传递演示"""
        demonstrate_parameter_passing()
        captured = capsys.readouterr()
        assert "参数传递机制演示" in captured.out
        assert "不可变对象" in captured.out

    def test_all_basic_demonstrations_run_without_error(self):
        """测试所有基础演示函数都能正常运行"""
        demonstrate_function_as_object()
        demonstrate_parameter_passing()
        demonstrate_parameter_types()
        demonstrate_scope_and_legb()
        demonstrate_closures()
        demonstrate_recursion()
        demonstrate_higher_order_functions()


class TestAdvancedExamples:
    """测试进阶示例"""

    def test_function_introspection_demonstration(self, capsys):
        """测试函数内省演示"""
        demonstrate_function_introspection()
        captured = capsys.readouterr()
        assert "函数内省演示" in captured.out
        assert "函数签名" in captured.out

    def test_advanced_decorators_demonstration(self, capsys):
        """测试高级装饰器演示"""
        demonstrate_advanced_decorators()
        captured = capsys.readouterr()
        assert "高级装饰器演示" in captured.out
        assert "带状态的装饰器" in captured.out

    def test_generator_functions_demonstration(self, capsys):
        """测试生成器函数演示"""
        demonstrate_generator_functions()
        captured = capsys.readouterr()
        assert "生成器函数演示" in captured.out
        assert "内存效率对比" in captured.out

    def test_function_factories_demonstration(self, capsys):
        """测试函数工厂演示"""
        demonstrate_function_factories()
        captured = capsys.readouterr()
        assert "函数工厂演示" in captured.out
        assert "验证器" in captured.out

    def test_all_advanced_demonstrations_run_without_error(self):
        """测试所有进阶演示函数都能正常运行"""
        demonstrate_function_introspection()
        demonstrate_advanced_decorators()
        demonstrate_generator_functions()
        demonstrate_function_factories()


if __name__ == "__main__":
    pytest.main([__file__])