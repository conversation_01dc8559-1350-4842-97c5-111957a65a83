"""
默认参数陷阱 - 基础示例

演示默认参数陷阱的基本概念和使用方法。
"""

from typing import List, Optional


def bad_append(item: int, bucket: List[int] = []):
    """错误示例：使用可变默认参数导致状态共享"""
    bucket.append(item)
    return bucket


def good_append(item: int, bucket: Optional[List[int]] = None):
    """正确示例：使用 None 作为哨兵，在函数体内创建新列表"""
    if bucket is None:
        bucket = []
    bucket.append(item)
    return bucket


def demonstrate_basic_concept():
    """演示基础概念：可变默认参数的陷阱"""
    print("=== 默认参数陷阱基础演示 ===")

    a = bad_append(1)
    b = bad_append(2)
    print(f"错误示例：两次调用共享同一列表 -> {a} vs {b}, 同一对象: {a is b}")

    a2 = good_append(1)
    b2 = good_append(2)
    print(f"正确示例：每次调用生成新列表 -> {a2} vs {b2}, 同一对象: {a2 is b2}")


def demonstrate_practical_usage():
    """演示实际使用：复杂默认参数与不可变默认值"""
    print("\n=== 实际使用演示 ===")

    def add_user(user: dict, users: Optional[List[dict]] = None) -> List[dict]:
        if users is None:
            users = []
        # 注意：复制入参，避免外部共享引用带来的副作用
        users.append(dict(user))
        return users

    u1_list = add_user({"name": "Alice"})
    u2_list = add_user({"name": "Bob"})
    print(f"两次独立调用不会互相污染: {u1_list} vs {u2_list}")


def demonstrate_common_patterns():
    """演示常见模式与最佳实践"""
    print("\n=== 常见模式演示 ===")

    # 1) 使用 None 作为默认值 + 在函数体内初始化
    print("1) 使用 None 作为默认并在函数体内初始化（推荐）")

    # 2) 若必须使用可变对象，可在函数入口显式复制
    from copy import deepcopy

    def append_safely(item: int, bucket: Optional[List[int]] = None):
        base = [] if bucket is None else list(bucket)
        base.append(item)
        return base

    x = append_safely(1)
    y = append_safely(2)
    print(f"显式复制避免复用: {x} vs {y}")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_basic_concept()
    demonstrate_practical_usage()
    demonstrate_common_patterns()
    
    print("\n=== 演示完成 ===")
    print("默认参数陷阱是Python编程的重要概念！")
