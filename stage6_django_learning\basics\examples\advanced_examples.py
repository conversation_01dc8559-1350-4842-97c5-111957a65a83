"""
Django 进阶基础示例（概念演示）：
- 中间件调用顺序（入站/出站）
- 表单与验证的基本流程
- ORM 查询表达能力概述
"""


def demonstrate_middleware_flow() -> None:
    print("=== 中间件调用顺序 ===")
    print("请求阶段：Middleware1.process_request → Middleware2.process_request → View")
    print("响应阶段：View → Middleware2.process_response → Middleware1.process_response")


def demonstrate_form_validation() -> None:
    print("=== 表单与验证 ===")
    print("forms.py: class ContactForm(forms.Form): name = forms.CharField(...)")
    print("视图：form = ContactForm(request.POST); form.is_valid(); form.cleaned_data")
    print("模板：{{ form.as_p }} 渲染并显示错误提示")


def demonstrate_orm_queries() -> None:
    print("=== ORM 查询基础 ===")
    print("MyModel.objects.filter(active=True).order_by('-created_at')[:10]")
    print("Q 对象与 F 表达式支持复杂查询与同字段计算")


if __name__ == "__main__":
    demonstrate_middleware_flow()
    demonstrate_form_validation()
    demonstrate_orm_queries()
    print("\n(概念演示，不依赖 Django 实际运行环境)")



