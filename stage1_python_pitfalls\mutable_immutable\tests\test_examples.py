"""
测试模块

测试相关的代码示例和概念。
"""

import pytest
from stage1_python_pitfalls.mutable_immutable.examples.basic_examples import (
    demonstrate_immutable_objects,
    demonstrate_mutable_objects,
    demonstrate_function_parameter_passing,
    demonstrate_assignment_behavior,
    demonstrate_common_pitfalls,
    modify_immutable,
    modify_mutable,
    append_to_list
)
from stage1_python_pitfalls.mutable_immutable.examples.advanced_examples import (
    demonstrate_advanced_concepts,
    demonstrate_performance_analysis,
    demonstrate_best_practices,
    demonstrate_common_pitfalls,
    demonstrate_real_world_applications
)


class TestBasicExamples:
    """测试基础示例"""
    
    def test_basic_demonstrations_run_without_error(self):
        """测试所有基础演示函数都能正常运行"""
        demonstrate_immutable_objects()
        demonstrate_mutable_objects()
        demonstrate_function_parameter_passing()
        demonstrate_assignment_behavior()
        demonstrate_common_pitfalls()
        modify_immutable()
        modify_mutable()
        append_to_list()


class TestAdvancedExamples:
    """测试进阶示例"""
    
    def test_advanced_demonstrations_run_without_error(self):
        """测试所有进阶演示函数都能正常运行"""
        demonstrate_advanced_concepts()
        demonstrate_performance_analysis()
        demonstrate_best_practices()
        demonstrate_common_pitfalls()
        demonstrate_real_world_applications()


if __name__ == "__main__":
    pytest.main([__file__])
