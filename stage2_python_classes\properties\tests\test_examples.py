import pytest
from stage2_python_classes.properties.examples.basic_examples import Temperature, Person, demonstrate_basic_usage


def test_temperature_validation_and_conversion():
    t = Temperature(0)
    assert t.fahrenheit == 32
    assert pytest.approx(t.kelvin, rel=1e-6) == 273.15

    with pytest.raises(TypeError):
        t.celsius = "hot"  # type: ignore
    with pytest.raises(ValueError):
        t.celsius = -274


def test_person_name_normalization_and_initials():
    p = Person("  alice   bob ")
    assert p.name == "Alice Bob"
    assert p.initials == "AB"

    with pytest.raises(ValueError):
        p.name = "  "


def test_demo_callable():
    demonstrate_basic_usage()

