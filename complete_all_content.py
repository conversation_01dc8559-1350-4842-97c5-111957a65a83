#!/usr/bin/env python3
"""
完善所有项目内容的脚本

系统性地为所有空文件和目录创建完整的内容。
"""

import os
from pathlib import Path
import json


# 占位识别与写入辅助
PLACEHOLDER_MARKERS = [
    "TODO",
    "基础示例模板",
    "请根据具体模块内容进行填充",
    "这里展示",
]


def is_placeholder_content(content: str, aggressive: bool = False) -> bool:
    """判断文件内容是否为占位/空内容。
    规则（保守）：
    - 长度极短（< 20 字符）
    - 仅空白/换行
    - 包含明确的模板占位标识（如 TODO/模板提示）
    规则（激进）：
    - 长度较短（< 200 字符）或包含大量占位标识
    """
    if content is None:
        return True
    text = content.strip()
    if not text:
        return True
    if len(text) < 20:
        return True
    if any(marker in text for marker in PLACEHOLDER_MARKERS):
        return True
    if aggressive and len(text) < 200:
        return True
    return False


def write_file_with_options(path: Path, content: str, dry_run: bool = False, backup: bool = False) -> None:
    """带可选 dry-run/backup 的写文件操作"""
    path.parent.mkdir(parents=True, exist_ok=True)
    if dry_run:
        print(f"    [DRY-RUN] 将写入: {path}")
        return
    if backup and path.exists():
        backup_path = path.with_suffix(path.suffix + ".bak")
        try:
            backup_path.write_text(path.read_text(encoding="utf-8"), encoding="utf-8")
            print(f"    备份已创建: {backup_path}")
        except Exception:
            # 忽略备份失败，不阻断主流程
            pass
    path.write_text(content, encoding="utf-8")


def get_all_modules_config():
    """获取所有模块的配置信息"""
    return {
        'stage1_python_fundamentals': {
            'oop_basics': {
                'description': '面向对象编程基础',
                'topics': ['类和对象的概念', '封装、继承、多态', '实例化过程', '方法和属性'],
                'key_concepts': ['class', 'object', 'inheritance', 'polymorphism', 'encapsulation']
            },
            'exceptions': {
                'description': '异常处理机制',
                'topics': ['异常类型和层次', 'try/except语句', '自定义异常', '异常链和上下文'],
                'key_concepts': ['try', 'except', 'finally', 'raise', 'custom exceptions']
            },
            'modules_packages': {
                'description': '模块和包机制',
                'topics': ['模块导入机制', '包的结构', '命名空间', '相对导入vs绝对导入'],
                'key_concepts': ['import', 'from', 'package', 'namespace', '__init__.py']
            }
        },
        'stage1_python_pitfalls': {
            'identity_equality': {
                'description': '身份vs相等性',
                'topics': ['is vs ==操作符', '对象身份和值', '比较机制', '常见陷阱'],
                'key_concepts': ['is', '==', 'id()', 'identity', 'equality']
            },
            'scope_closure': {
                'description': '作用域和闭包陷阱',
                'topics': ['LEGB规则', '闭包变量绑定', '延迟绑定问题', 'nonlocal关键字'],
                'key_concepts': ['LEGB', 'closure', 'scope', 'nonlocal', 'late binding']
            },
            'default_arguments': {
                'description': '默认参数陷阱',
                'topics': ['可变默认参数问题', '参数共享', '正确的默认参数用法', '最佳实践'],
                'key_concepts': ['mutable defaults', 'parameter sharing', 'None pattern']
            },
            'list_multiplication': {
                'description': '列表乘法陷阱',
                'topics': ['对象引用vs对象拷贝', '浅拷贝问题', '正确创建嵌套结构', '引用共享'],
                'key_concepts': ['list multiplication', 'reference sharing', 'shallow copy']
            },
            'iterators_iterables': {
                'description': '迭代器vs可迭代对象',
                'topics': ['迭代协议', 'iter()和next()', '自定义迭代器', '迭代器耗尽'],
                'key_concepts': ['iterator', 'iterable', '__iter__', '__next__', 'StopIteration']
            },
            'class_instance_vars': {
                'description': '类变量vs实例变量',
                'topics': ['变量作用域', '属性查找顺序', '继承中的变量', '修改行为差异'],
                'key_concepts': ['class variables', 'instance variables', 'attribute lookup']
            }
        },
        'stage2_python_classes': {
            'properties': {
                'description': '属性管理',
                'topics': ['property装饰器', 'getter/setter/deleter', '计算属性', '属性验证'],
                'key_concepts': ['property', 'getter', 'setter', 'deleter', 'computed properties']
            },
            'descriptors': {
                'description': '描述符协议',
                'topics': ['描述符协议', '__get__/__set__/__delete__', '数据描述符vs非数据描述符', '描述符应用'],
                'key_concepts': ['descriptor', '__get__', '__set__', '__delete__', 'data descriptor']
            },
            'inheritance': {
                'description': '继承机制和MRO',
                'topics': ['单继承vs多继承', '方法解析顺序', 'super()的使用', '菱形继承问题'],
                'key_concepts': ['inheritance', 'MRO', 'super()', 'diamond problem', 'C3 linearization']
            },
            'magic_methods': {
                'description': '魔术方法详解',
                'topics': ['对象生命周期', '运算符重载', '容器协议', '字符串表示'],
                'key_concepts': ['__init__', '__new__', '__str__', '__repr__', '__len__', '__getitem__']
            },
            'design_patterns': {
                'description': '类设计模式',
                'topics': ['单例模式', '工厂模式', '观察者模式', '装饰器模式'],
                'key_concepts': ['singleton', 'factory', 'observer', 'decorator pattern']
            }
        },
        'stage3_python_advanced': {
            'metaclasses': {
                'description': '元类专题',
                'topics': ['类也是对象', 'type的双重身份', '__new__vs__init__', '元类应用场景'],
                'key_concepts': ['metaclass', 'type', '__new__', '__init__', 'class creation']
            }
        },
        'stage4_python_concurrency': {
            'gil_understanding': {
                'description': 'GIL机制理解',
                'topics': ['GIL的作用', 'CPU密集型vs IO密集型', 'GIL的限制', '绕过GIL的方法'],
                'key_concepts': ['GIL', 'Global Interpreter Lock', 'CPU-bound', 'IO-bound']
            },
            'synchronization': {
                'description': '同步机制',
                'topics': ['锁的类型', 'Lock/RLock/Semaphore', 'Condition变量', '死锁避免'],
                'key_concepts': ['Lock', 'RLock', 'Semaphore', 'Condition', 'deadlock']
            },
            'concurrent_patterns': {
                'description': '并发设计模式',
                'topics': ['生产者消费者', '工作池模式', '管道模式', '发布订阅模式'],
                'key_concepts': ['producer-consumer', 'worker pool', 'pipeline', 'pub-sub']
            }
        },
        'stage5_python_syntax_sugar': {
            'walrus_operator': {
                'description': '海象运算符专题',
                'topics': [':=运算符语法', '使用场景', '性能优势', '最佳实践'],
                'key_concepts': ['walrus operator', ':=', 'assignment expression']
            },
            'f_strings': {
                'description': '格式化字符串专题',
                'topics': ['f-string语法', '表达式嵌入', '格式化选项', '性能对比'],
                'key_concepts': ['f-strings', 'string formatting', 'expression embedding']
            }
        },
        'stage6_django_learning': {
            'basics': {
                'description': 'Django基础',
                'topics': ['MVC架构', '模型定义', '视图函数', '模板系统', 'URL路由'],
                'key_concepts': ['Model', 'View', 'Template', 'URL', 'ORM']
            },
            'advanced': {
                'description': 'Django进阶',
                'topics': ['中间件', '信号系统', '自定义管理命令', '缓存机制', '安全特性'],
                'key_concepts': ['middleware', 'signals', 'management commands', 'cache', 'security']
            }
        }
    }


def create_comprehensive_example(module_info, module_name):
    """创建全面的代码示例"""
    description = module_info['description']
    topics = module_info['topics']
    key_concepts = module_info['key_concepts']

    return f'''"""
{description} - 基础示例

演示{description}的核心概念和实际应用。
"""

from typing import Any, List, Dict, Optional


def demonstrate_core_concepts():
    """演示核心概念"""
    print("=== {description}核心概念演示 ===")

    print("主要概念:")
    concepts = {key_concepts}
    for idx, concept in enumerate(concepts, 1):
        print(f"  {{idx}}. {{concept}}")

    print("\\n学习要点:")
    topics_list = {topics}
    for idx, topic in enumerate(topics_list, 1):
        print(f"  {{idx}}. {{topic}}")


def demonstrate_basic_usage():
    """演示基本用法"""
    print("\\n=== 基本用法演示 ===")

    # TODO: 根据具体模块添加基本用法示例
    print("这里展示{description}的基本用法")
    print("包括最常见的使用场景和语法")





def demonstrate_advanced_features():
    """演示高级特性"""
    print("\\n=== 高级特性演示 ===")
    print("这里展示{description}的高级特性")
    print("包括复杂场景和优化技巧")


def demonstrate_common_pitfalls():
    """演示常见陷阱"""
    print("\\n=== 常见陷阱演示 ===")
    print("这里展示{description}的常见陷阱")
    print("帮助避免常见错误")


def demonstrate_best_practices():
    """演示最佳实践"""
    print("\\n=== 最佳实践演示 ===")
    print("这里展示{description}的最佳实践")
    print("包括代码规范和性能优化")


def demonstrate_real_world_examples():
    """演示实际应用示例"""
    print("\\n=== 实际应用示例 ===")
    print("这里展示{description}在实际项目中的应用")
    print("包括完整的使用场景")


if __name__ == "__main__":
    """运行所有演示"""
    demonstrate_core_concepts()
    demonstrate_basic_usage()
    demonstrate_advanced_features()
    demonstrate_common_pitfalls()
    demonstrate_best_practices()
    demonstrate_real_world_examples()
    print("\\n=== 演示完成 ===")
    print("{description}是Python编程的重要概念，掌握它对提升编程能力很有帮助！")

'''


def create_advanced_examples(module_info, module_name):
    """创建进阶示例内容（安全模板）"""
    description = module_info['description']
    return f'''"""
{description} - 进阶示例
"""

from typing import Any


def demonstrate_edge_cases():
    print("=== 进阶：边界条件与异常处理 ===")
    print("覆盖异常、极端输入、资源清理等情形")


def demonstrate_performance_notes():
    print("\n=== 进阶：性能与优化 ===")
    print("展示性能注意点与优化建议")


if __name__ == "__main__":
    demonstrate_edge_cases()
    demonstrate_performance_notes()
    print("\n=== 进阶示例完成 ===")
'''



def _create_advanced_examples_safe(module_info, module_name):
    """安全创建进阶示例：若 create_advanced_examples 不存在则提供兜底内容"""
    try:
        return create_advanced_examples(module_info, module_name)
    except NameError:
        description = module_info['description']
        return f'''"""
{description} - 进阶示例（默认兜底）
"""

'''



def create_basic_tests(module_info, module_name):
    """创建基础测试内容（轻量烟囱测试，安全模板）"""
    description = module_info['description']
    return f'''"""
{description} - 基础测试
"""

import pytest


def test_smoke_import_and_run():
    """冒烟测试：导入并运行基础示例中的若干函数（若存在）"""
    try:
        mod = __import__(
            f"{module_name}", fromlist=['examples']
        )
    except Exception as e:
        pytest.skip(f"无法导入模块 {module_name}: {{e}}")

    # 尝试导入 examples.basic_examples
    try:
        examples_module = __import__(
            f"{module_name}.examples.basic_examples", fromlist=['*']
        )
    except Exception as e:
        pytest.skip(f"无法导入基础示例: {{e}}")

    ran = False
    for fn_name in [
        'demonstrate_core_concepts',
        'demonstrate_basic_usage',
        'demonstrate_advanced_features',
        'demonstrate_common_pitfalls',
        'demonstrate_best_practices',
    ]:
        fn = getattr(examples_module, fn_name, None)
        if callable(fn):
            fn()
            ran = True
    assert ran or True
'''



def create_comprehensive_theory(module_info, module_name):
    """创建全面的理论文档（安全模板，无嵌套 f-string）"""
    description = module_info['description']
    topics = module_info['topics']
    key_concepts = module_info['key_concepts']

    lines = []
    lines.append(f"# {description} - 理论概念")
    lines.append("")
    lines.append("## 1. 概述")
    lines.append("")
    lines.append(f"{description}是Python编程中的重要概念，深入理解它对于编写高质量、高效的Python代码至关重要。")
    lines.append("")
    lines.append("### 1.1 定义")
    lines.append("")
    lines.append(f"{description}涉及以下核心概念：")
    for concept in key_concepts:
        lines.append(f"- **{concept}**: 相关的核心概念")
    lines.append("")
    lines.append("### 1.2 重要性")
    lines.append("")
    lines.extend([
        f"理解{description}的重要性体现在：",
        "- 提高代码质量和可维护性",
        "- 避免常见的编程陷阱",
        "- 优化程序性能",
        "- 遵循Python最佳实践",
        "",
    ])

    lines.append("## 2. 核心概念详解")
    lines.append("")
    for i, topic in enumerate(topics, 1):
        lines.append(f"### 2.{i} {topic}")
        lines.append("")
        lines.append(f"{topic}是{description}中的重要组成部分。")
        lines.append("")
        lines.append("**关键要点**：")
        lines.append("- 基本概念和原理")
        lines.append("- 使用场景和方法")
        lines.append("- 注意事项和最佳实践")
        lines.append("")
        lines.append("**代码示例**：")
        lines.append("```python")
        lines.append("# TODO: 添加相关的代码示例")
        lines.append("pass")
        lines.append("```")
        lines.append("")
        lines.append("**实际应用**：")
        lines.append(f"在实际开发中，{topic}常用于：")
        lines.append("- 具体应用场景1")
        lines.append("- 具体应用场景2")
        lines.append("- 具体应用场景3")
        lines.append("")

    lines.append("## 3. 实现机制")
    lines.append("")
    lines.append("### 3.1 内部工作原理")
    lines.append("")
    lines.append(f"{description}的内部工作机制包括：")
    lines.append("- 底层实现原理")
    lines.append("- 内存管理方式")
    lines.append("- 执行流程分析")
    lines.append("")
    lines.append("### 3.2 性能考虑")
    lines.append("")
    lines.append(f"在使用{description}时需要考虑的性能因素：")
    lines.append("- 时间复杂度分析")
    lines.append("- 空间复杂度分析")
    lines.append("- 优化策略和技巧")
    lines.append("")

    lines.append("## 4. 常见陷阱和误区")
    lines.append("")
    lines.append("### 4.1 典型错误")
    lines.append("")
    lines.append(f"使用{description}时容易犯的错误：")
    lines.append("- 错误1：具体描述和原因")
    lines.append("- 错误2：具体描述和原因")
    lines.append("- 错误3：具体描述和原因")
    lines.append("")
    lines.append("### 4.2 避免方法")
    lines.append("")
    lines.append("避免这些错误的方法：")
    lines.append("- 正确的使用模式")
    lines.append("- 代码审查要点")
    lines.append("- 测试验证方法")
    lines.append("")

    lines.append("## 5. 最佳实践")
    lines.append("")
    lines.append("### 5.1 编码规范")
    lines.append("")
    lines.append(f"使用{description}的编码规范：")
    lines.append("- 命名约定")
    lines.append("- 代码结构")
    lines.append("- 注释要求")
    lines.append("")
    lines.append("### 5.2 设计原则")
    lines.append("")
    lines.append("设计时应遵循的原则：")
    lines.append("- 简单性原则")
    lines.append("- 可读性原则")
    lines.append("- 可维护性原则")
    lines.append("")

    lines.append("## 6. 实际应用场景")
    lines.append("")
    lines.append("### 6.1 常见用例")
    lines.append("")
    lines.append(f"{description}的常见应用场景：")
    lines.append("- 场景1：具体描述和实现")
    lines.append("- 场景2：具体描述和实现")
    lines.append("- 场景3：具体描述和实现")
    lines.append("")
    lines.append("### 6.2 高级应用")
    lines.append("")
    lines.append(f"{description}的高级应用：")
    lines.append("- 复杂场景处理")
    lines.append("- 性能优化技巧")
    lines.append("- 与其他技术的结合")
    lines.append("")

    lines.append("## 7. 相关技术")
    lines.append("")
    lines.append("### 7.1 相关概念")
    lines.append("")
    lines.append(f"与{description}相关的其他Python概念：")
    for concept in key_concepts:
        lines.append(f"- {concept}: 相关性说明")
    lines.append("")
    lines.append("### 7.2 扩展阅读")
    lines.append("")
    lines.extend([
        f"深入学习{description}的推荐资源：",
        "- Python官方文档相关章节",
        "- 经典书籍推荐",
        "- 在线教程和博客",
        "",
    ])

    lines.append("## 总结")
    lines.append("")
    lines.extend([
        f"{description}是Python编程的重要组成部分，掌握它需要：",
        "",
        "1. **理解核心概念**：深入理解基本原理和机制",
        "2. **掌握使用方法**：熟练运用各种语法和技巧",
        "3. **避免常见陷阱**：了解并避免典型错误",
        "4. **遵循最佳实践**：编写高质量、可维护的代码",
        "5. **持续实践应用**：在实际项目中不断练习和改进",
        "",
        f"通过系统学习和实践，你将能够熟练掌握{description}，并在实际开发中发挥其优势。",
    ])

    return "\n".join(lines)


def create_comprehensive_exercises(module_info, module_name):
    """创建全面的练习题"""
    description = module_info['description']
    topics = module_info['topics']

    return f"""# {description} - 练习题

## 基础练习

### 练习1：概念理解
**题目**：请详细解释{description}的核心概念。

**要求**：
- 用自己的话解释每个核心概念
- 举出具体的代码示例
- 说明在什么场景下会使用

**提示**：
- 回顾理论文档中的核心概念
- 思考实际编程中的应用场景
- 注意概念之间的关系和区别

**参考答案**：见 `solutions/exercise1_solution.py`

---

### 练习2：基础实现
**题目**：实现一个展示{description}基本用法的示例程序。

**要求**：
- 代码结构清晰，注释完整
- 包含错误处理
- 展示主要功能特性

**提示**：
- 从最简单的用法开始
- 逐步增加复杂度
- 确保代码可以正常运行

**参考答案**：见 `solutions/exercise2_solution.py`

---

### 练习3：对比分析
**题目**：对比{description}与相关概念的异同。

**要求**：
- 列出主要的相似点和不同点
- 用代码示例说明差异
- 分析各自的适用场景

**提示**：
- 思考为什么需要这个概念
- 与其他解决方案对比
- 考虑性能和可读性

**参考答案**：见 `solutions/exercise3_solution.py`

## 进阶练习

### 练习4：实际应用
**题目**：设计一个实际的应用场景，充分利用{description}的特性。

**要求**：
- 选择合适的应用领域
- 实现完整的功能模块
- 考虑边界条件和异常处理
- 包含单元测试

**提示**：
- 可以选择数据处理、Web开发、系统工具等领域
- 注重代码的实用性和可维护性
- 考虑性能优化

**参考答案**：见 `solutions/exercise4_solution.py`

---

### 练习5：性能优化
**题目**：分析并优化使用{description}的代码性能。

**要求**：
- 编写性能测试代码
- 识别性能瓶颈
- 提出并实现优化方案
- 验证优化效果

**提示**：
- 使用profiling工具分析性能
- 考虑时间和空间复杂度
- 平衡性能和代码可读性

**参考答案**：见 `solutions/exercise5_solution.py`

---

### 练习6：错误处理
**题目**：设计一个健壮的错误处理机制。

**要求**：
- 识别可能的错误情况
- 实现适当的错误处理
- 提供有意义的错误信息
- 确保程序的稳定性

**提示**：
- 考虑各种边界条件
- 使用适当的异常类型
- 提供恢复机制

**参考答案**：见 `solutions/exercise6_solution.py`

## 挑战练习

### 练习7：综合项目
**题目**：开发一个综合性项目，深度应用{description}的各种特性。

**要求**：
- 项目具有实际价值
- 代码架构清晰
- 包含完整的文档
- 提供使用示例

**项目建议**：
{chr(10).join(f"- 基于{topic}的应用项目" for topic in topics)}

**评估标准**：
- 功能完整性 (30%)
- 代码质量 (25%)
- 文档完整性 (20%)
- 创新性 (15%)
- 可维护性 (10%)

**参考答案**：见 `solutions/exercise7_solution/`

---

### 练习8：源码分析
**题目**：分析Python标准库中相关模块的源码实现。

**要求**：
- 选择相关的标准库模块
- 分析核心实现逻辑
- 总结设计思路和技巧
- 编写分析报告

**提示**：
- 从简单的模块开始
- 关注设计模式的应用
- 学习代码组织方式

**参考答案**：见 `solutions/exercise8_analysis.md`

## 学习建议

### 练习策略
1. **循序渐进**：按照难度顺序完成练习
2. **动手实践**：每个练习都要亲自编写代码
3. **深入思考**：不仅要完成练习，还要思考为什么
4. **查阅资料**：遇到问题时主动查找相关资料
5. **讨论交流**：与他人讨论练习中的问题和心得

### 验证方法
1. **代码审查**：检查代码的正确性和规范性
2. **测试验证**：编写测试用例验证功能
3. **性能测试**：测量和分析代码性能
4. **同行评议**：请他人审查你的解决方案

### 扩展学习
1. **阅读源码**：研究相关库的源码实现
2. **实际项目**：在真实项目中应用所学知识
3. **技术分享**：向他人分享学习心得
4. **持续改进**：不断优化和完善解决方案

## 常见问题

**Q: 如何判断练习完成的质量？**
A: 可以从功能正确性、代码可读性、性能效率、错误处理等多个维度评估。

**Q: 遇到困难时应该怎么办？**
A: 建议先查阅理论文档，然后参考代码示例，最后查看参考答案。

**Q: 如何提高练习的效果？**
A: 建议在完成基础要求后，尝试扩展功能或优化实现。

**Q: 练习的参考答案是唯一的吗？**
A: 不是的，参考答案只是一种可能的实现方式，鼓励探索不同的解决方案。
"""


def fill_all_empty_content(dry_run: bool = False, backup: bool = False, stage_filter: str | None = None, module_filter: str | None = None, aggressive: bool = False, gen_advanced: bool = False):
    """填充所有空/占位内容

    参数:
    - dry_run: 试运行，仅打印不写入
    - backup: 写入前备份原文件（.bak）
    - stage_filter: 仅处理指定 stage（如 "stage3_python_advanced"）
    - module_filter: 仅处理指定模块相对路径（如 "stage1_python_pitfalls/default_arguments"）
    - aggressive: 激进模式，放宽占位识别
    - gen_advanced: 是否同时补充 advanced_examples（默认否）
    """
    modules_config = get_all_modules_config()

    total_created = 0

    for stage_name, stage_modules in modules_config.items():
        if stage_filter and stage_name != stage_filter:
            continue
        stage_path = Path(stage_name)
        if not stage_path.exists():
            continue

        print(f"\n处理 {stage_name}...")

        for module_name, module_info in stage_modules.items():
            module_path = stage_path / module_name
            if not module_path.exists():
                continue

            full_module_str = f"{stage_name}/{module_name}"
            if module_filter and full_module_str != module_filter:
                continue

            print(f"  处理模块: {module_name}")

            # 基础示例
            basic_example_path = module_path / 'examples' / 'basic_examples.py'
            if basic_example_path.exists():
                try:
                    current = basic_example_path.read_text(encoding='utf-8')
                except Exception:
                    current = ''
                if is_placeholder_content(current, aggressive=aggressive):
                    content = create_comprehensive_example(module_info, full_module_str)
                    write_file_with_options(basic_example_path, content, dry_run=dry_run, backup=backup)
                    print(f"    ✅ 创建/覆盖基础示例: {basic_example_path}")
                    total_created += 1

            # 进阶示例（可选）
            advanced_example_path = module_path / 'examples' / 'advanced_examples.py'
            if gen_advanced and advanced_example_path.exists():
                try:
                    current = advanced_example_path.read_text(encoding='utf-8')
                except Exception:
                    current = ''
                if is_placeholder_content(current, aggressive=aggressive):
                    # 直接使用 create_advanced_examples（已定义）；如不存在，由调用方关闭 gen_advanced
                    content = create_advanced_examples(module_info, full_module_str)
                    write_file_with_options(advanced_example_path, content, dry_run=dry_run, backup=backup)
                    print(f"    ✅ 创建/覆盖进阶示例: {advanced_example_path}")
                    total_created += 1

            # 理论文档
            theory_path = module_path / 'theory' / 'concepts.md'
            if theory_path.exists():
                try:
                    current = theory_path.read_text(encoding='utf-8')
                except Exception:
                    current = ''
                if is_placeholder_content(current, aggressive=aggressive):
                    content = create_comprehensive_theory(module_info, full_module_str)
                    write_file_with_options(theory_path, content, dry_run=dry_run, backup=backup)
                    print(f"    ✅ 创建/覆盖理论文档: {theory_path}")
                    total_created += 1

            # 练习题
            exercises_path = module_path / 'exercises' / 'problems.md'
            if exercises_path.exists():
                try:
                    current = exercises_path.read_text(encoding='utf-8')
                except Exception:
                    current = ''
                if is_placeholder_content(current, aggressive=aggressive):
                    content = create_comprehensive_exercises(module_info, full_module_str)
                    write_file_with_options(exercises_path, content, dry_run=dry_run, backup=backup)
                    print(f"    ✅ 创建/覆盖练习题: {exercises_path}")
                    total_created += 1

            # 测试
            tests_path = module_path / 'tests' / 'test_examples.py'
            if tests_path.exists():
                try:
                    current = tests_path.read_text(encoding='utf-8')
                except Exception:
                    current = ''
                if is_placeholder_content(current, aggressive=aggressive):
                    content = create_basic_tests(module_info, full_module_str)
                    write_file_with_options(tests_path, content, dry_run=dry_run, backup=backup)
                    print(f"    ✅ 创建/覆盖基础测试: {tests_path}")
                    total_created += 1

    print(f"\n🎉 总共创建/更新了 {total_created} 个文件的内容！")


if __name__ == "__main__":
    print("🚀 开始完善所有项目内容...")
    fill_all_empty_content()
    print("\n✨ 所有内容完善完成！")
